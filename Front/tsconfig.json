{
  "compilerOptions": {
    "lib": [
      "DOM",
      "ES2020"
    ],
    "jsx": "preserve",
    "target": "ES2020",
    "noEmit": true,
    "skipLibCheck": true,
    "useDefineForClassFields": true,
    /* modules */
    "module": "ESNext",
    "isolatedModules": true,
    "resolveJsonModule": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "assets/*": [
        "src/assets/*"
      ],
      "pages/*": [
        "src/pages/*"
      ],
      "components/*": [
        "src/components/*"
      ],
      "service/*": [
        "src/service/*"
      ],
      "store/*": [
        "src/store/*"
      ],
      "utils/*": [
        "src/utils/*"
      ]
    }
  },
  "include": [
    "src"
  ]
}