## 🛠️ 启动项目

- 安装 [git](https://git-scm.com/downloads)
- 安装 [node](http://nodejs.cn)，版本 >= 16
- 安装 [pnpm](https://pnpm.io/zh) pnpm install -g pnpm@7，
- 安装 vscode 插件 [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)、[Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
- 安装 tailwindcss 插件 [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)

```bash
npm install -g pnpm@7

// 安装依赖
pnpm install

// 启动
pnpm dev

// 打包
pnpm build
```

### 网页全屏

需要在网页加上参数 fullScreen=true
http://xxx.com/aimkt/home?fullScreen=true
