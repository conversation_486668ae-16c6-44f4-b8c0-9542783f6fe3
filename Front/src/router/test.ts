import { RouteConfig } from "vue-router";
import TestFilter from "../pages/test/TestFilter.vue";
import TestEvent from "../pages/test/TestEvent.vue";
import TestActioncollective from "../pages/test/TestActioncollective.vue";
import Test from "../pages/test/Test.vue";
import TestSegment from "../pages/test/TestSegment.vue";
import TestLabel from "../pages/test/TestLabel.vue";
export const testRoutes: RouteConfig = {
  path: "/test",
  name: "Test",
  redirect: "/test/filter",
  component: Test,
  children: [
    {
      path: "filter",
      name: "TestFilter",
      component: TestFilter,
      meta: {
        title: "测试Filter"
      }
    },
    {
      path: "event",
      name: "TestEvent",
      component: TestEvent,
      meta: {
        title: "测试事件过滤"
      }
    },
    {
      path: "actioncollective",
      name: "TestActioncollective",
      component: TestActioncollective
    },
    {
      path: "segment",
      name: "TestSegment",
      component: TestSegment
    },
    {
      path: "label",
      name: "TestLabel",
      component: TestLabel
    }
  ]
};
