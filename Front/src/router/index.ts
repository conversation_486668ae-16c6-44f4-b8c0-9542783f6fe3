import Vue from "vue";
import VueRouter from "vue-router";

import Gateway from "@/pages/index.vue";
import HomeLayout from "../pages/home/<USER>";
import Home from "../pages/home/<USER>";
import About from "../pages/home/<USER>";
import Components from "../pages/home/<USER>";
import Error from "../pages/error.vue";

import { oppRoutes } from "./oppRouter";
import { testRoutes } from "./test";
import { CustomRouteConfig, processRoutes } from "./routeUtils";

Vue.use(VueRouter);

/**
 *  todo: home下的路由 有的需要全屏展示, 需要在meta中设置skipLayout: true 就不会展示layout
 *  新建路由最好统一 create 详情页detail
 */
const homeRoutes: CustomRouteConfig[] = [
  {
    path: "",
    name: "Home",
    component: Home
  },
  {
    path: "about",
    name: "About",
    component: About
  },
  {
    path: "components",
    name: "Components",
    component: Components
  },
  // todo 暂时写成一级路由
  ...oppRoutes,
  {
    path: "pred_metrics",
    name: "IntelligentResPre",
    component: () => import("@/pages/home/<USER>/IntelligentResPre.vue"),
    meta: {
      title: "智能指标预测"
    }
  },
  {
    path: "pred_metrics/detail",
    name: "IntelligentResPreDetail",
    component: () => import("@/pages/home/<USER>/IntelligentResPreDetail.vue"),
    meta: {
      title: "智能结果预测详情",
      skipLayout: true
    }
  }
];

const { layoutChildren, topLevelRoutes } = processRoutes(homeRoutes);

// logRouteConfig(layoutChildren, topLevelRoutes);

const routes = [
  {
    path: "/",
    name: "Gateway",
    component: Gateway
  },
  {
    path: "/home",
    component: HomeLayout,
    children: layoutChildren
  },
  testRoutes,
  ...topLevelRoutes,
  {
    path: "*",
    name: "Error",
    component: Error
  }
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});

const isInIframe = window.self !== window.top;

router.beforeEach((to, _from, next) => {
  const isFullScreen = to.query.fullScreen === "true" || isInIframe;
  Vue.prototype.$isFullScreen = isFullScreen;

  next();
});

export default router;
