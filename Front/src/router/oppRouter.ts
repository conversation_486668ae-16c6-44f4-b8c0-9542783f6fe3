import { CustomRouteConfig } from "./routeUtils";
// 实时商机
const real_time_rouer: CustomRouteConfig[] = [
  {
    path: "opp/real_time_opp",
    name: "RealTime",
    component: () => import("@/pages/home/<USER>/RealTime/index.vue"),
    meta: {
      title: "实时商机"
    }
  },
  {
    path: "opp/real_time_opp/create",
    name: "RealTimeAdd",
    component: () => import("@/pages/home/<USER>/RealTime/create.vue"),
    meta: {
      title: "新建商机",
      skipLayout: true
    }
  },
  {
    path: "opp/real_time_opp/edit/:id",
    name: "RealTimeEdit",
    component: () => import("@/pages/home/<USER>/RealTime/create.vue"),
    meta: {
      title: "编辑商机",
      skipLayout: true
    }
  },
  {
    path: "opp/real_time_opp/detail",
    name: "RealTimeDatile",
    component: () => import("@/pages/home/<USER>/RealTime/detail.vue"),
    meta: {
      title: "商机详情",
      skipLayout: true
    }
  }
];
// 批量商机 (离线商机)
const offline_rouer: CustomRouteConfig[] = [
  {
    path: "opp/offline_opp",
    name: "Offline",
    component: () => import("@/pages/home/<USER>/Offline/index.vue"),
    meta: {
      title: "离线商机"
    }
  },
  {
    path: "opp/offline_opp/create",
    name: "OfflineAdd",
    component: () => import("@/pages/home/<USER>/Offline/create.vue"),
    meta: {
      title: "新建商机",
      skipLayout: true
    }
  },
  {
    path: "opp/offline_opp/edit/:id",
    name: "OfflineEdit",
    component: () => import("@/pages/home/<USER>/Offline/create.vue"),
    meta: {
      title: "编辑商机",
      skipLayout: true
    }
  },
  {
    path: "opp/offline_opp/detail",
    name: "OfflineDatile",
    component: () => import("@/pages/home/<USER>/Offline/detail.vue"),
    meta: {
      title: "商机详情",
      skipLayout: true
    }
  }
];

// 模型商机
const model_rouer: CustomRouteConfig[] = [
  {
    path: "opp/model_opp",
    component: () => import("@/pages/home/<USER>/Model/index.vue"),
    meta: {
      title: "模型商机"
    }
  },
  {
    path: "opp/model_opp/create",
    name: "ModelAdd",
    component: () => import("@/pages/home/<USER>/Model/create.vue"),
    meta: {
      title: "新建商机",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/edit/:id",
    name: "ModelEdit",
    component: () => import("@/pages/home/<USER>/Model/create.vue"),
    meta: {
      title: "编辑商机",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/detail",
    name: "ModelDatile",
    component: () => import("@/pages/home/<USER>/Model/detail.vue"),
    meta: {
      title: "商机详情",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/model/scene_select",
    name: "ModelScene",
    component: () => import("@/pages/home/<USER>/Model/modelScene.vue"),
    meta: {
      title: "模型场景",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/model/model_select/:id",
    name: "ModelScene",
    component: () => import("@/pages/home/<USER>/Model/modelList.vue"),
    meta: {
      title: "模型列表",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/model/task/:id",
    name: "ModelScene",
    component: () => import("@/pages/home/<USER>/Model/taskList.vue"),
    meta: {
      title: "模型任务",
      skipLayout: true
    }
  },
  {
    path: "/opp/model_opp/model/task/create/:id",
    name: "ModelTaskAdd",
    component: () => import("@/pages/home/<USER>/Model/taskCreate.vue"),
    meta: {
      title: "新建模型任务",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/model/task/edit/:id",
    name: "ModelEdit",
    component: () => import("@/pages/home/<USER>/Model/taskCreate.vue"),
    meta: {
      title: "编辑模型任务",
      skipLayout: true
    }
  },
  {
    path: "/opp/model_opp/model/task/detail/:id",
    name: "ModelDatile",
    component: () => import("@/pages/home/<USER>/Model/taskDetail.vue"),
    meta: {
      title: "模型任务详情",
      skipLayout: true
    }
  },
  {
    path: "opp/model_opp/model/task/batch/detail",
    name: "ModelTaskBatchDetail",
    component: () => import("@/pages/home/<USER>/Model/Task/Batch/detail.vue"),
    meta: {
      title: "任务批次详情",
      skipLayout: true
    }
  }
];
// 人群扩散商机
const crowd_rouer: CustomRouteConfig[] = [
  {
    path: "opp/crowd_opp",
    component: () => import("@/pages/home/<USER>/Crowd/index.vue"),
    meta: {
      title: "人群扩散商机"
    }
  },
  {
    path: "opp/crowd_opp/create",
    name: "CrowdAdd",
    component: () => import("@/pages/home/<USER>/Crowd/create.vue"),
    meta: {
      title: "新建商机",
      skipLayout: true
    }
  },
  {
    path: "opp/crowd_opp/edit/:id",
    name: "CrowdEdit",
    component: () => import("@/pages/home/<USER>/Crowd/create.vue"),
    meta: {
      title: "编辑商机",
      skipLayout: true
    }
  },
  {
    path: "opp/crowd_opp/detail",
    name: "CrowdDatile",
    component: () => import("@/pages/home/<USER>/Crowd/detail.vue"),
    meta: {
      title: "商机详情",
      skipLayout: true
    }
  },
  {
    path: "/opp/model_opp/model/task/create",
    name: "ModelTaskAdd",
    component: () => import("@/pages/home/<USER>/Model/taskCreate.vue"),
    meta: {
      title: "新建模型任务商机",
      skipLayout: true
    }
  }
];

// 自定义挖掘商机
const custom_rouer: CustomRouteConfig[] = [
  {
    path: "opp/custom_opp",
    component: () => import("@/pages/home/<USER>/Custom/index.vue"),
    meta: {
      title: "自定义挖掘商机"
    }
  },
  {
    path: "opp/custom_opp/create",
    name: "CustomAdd",
    component: () => import("@/pages/home/<USER>/Custom/create.vue"),
    meta: {
      title: "新建商机",
      skipLayout: true
    }
  },
  {
    path: "opp/custom_opp/edit/:id",
    name: "CustomEdit",
    component: () => import("@/pages/home/<USER>/Custom/create.vue"),
    meta: {
      title: "编辑商机",
      skipLayout: true
    }
  },
  {
    path: "opp/custom_opp/detail",
    name: "CustomDatile",
    component: () => import("@/pages/home/<USER>/Custom/detail.vue"),
    meta: {
      title: "商机详情",
      skipLayout: true
    }
  }
];

export const oppRoutes: CustomRouteConfig[] = [
  ...real_time_rouer,
  ...offline_rouer,
  ...model_rouer,
  ...crowd_rouer,
  ...custom_rouer
];
