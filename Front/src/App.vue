<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 11:12:35
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月17 15:18:30
 * @Description  : 
-->
<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <Layout />
    </a-config-provider>
  </div>
</template>

<script>
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import enUS from "ant-design-vue/lib/locale-provider/en_US";

import { Layout } from "./pages/frame";

export default {
  name: "App",
  components: {
    Layout,
  },
  data() {
    return {
      lang: "zh-cn",
    };
  },
  computed: {
    locale() {
      return this.lang === "zh-cn" ? zhCN : enUS;
    },
  },
  mounted() {},
};
</script>

<style scoped>
#app {
  width: 100%;
  height: 100%;
}
</style>
