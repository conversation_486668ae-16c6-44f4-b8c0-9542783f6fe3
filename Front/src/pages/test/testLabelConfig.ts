const findCategoryByProjectId = (data: any) => {
  return {};
};
const getTagList = (data: any) => [
  {
    createTime: 1690972078000,
    updateTime: 1690972078000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 678,
    name: "0801angd测试标签",
    displayName: "0801angd测试标签",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select p1 as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 13,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类2/测试三级分类/",
    whetherEnum: false
  },
  {
    createTime: 1723631723000,
    updateTime: 1723631723000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 728,
    name: "0814",
    displayName: "姓名",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as `value`,device_id as `dt_id` from `wolf`.`user_sample` where gender = '0'"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false
  },
  {
    createTime: 1645775054000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 587,
    name: "1",
    displayName: "1",
    remark: "顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as value, device_id as dt_id from wolf.user_sample limit 10"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "DOUBLE",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1671590941000,
    updateTime: 1682508951000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 622,
    name: "1221_test_user_label",
    displayName: "1221_test_user_label",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as `value`, dt_id from `wolf`.`test_user_01` where scenario_code = '0'"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1671590949000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 13,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类2/测试三级分类/",
    whetherEnum: true,
    tagCalcTime: 1682352000000
  },
  {
    createTime: 1713768501000,
    updateTime: 1715327663000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 713,
    name: "123",
    displayName: "qweqeqe",
    remark: "qeqeq",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "ABS(123)"
    },
    lastCalcStatus: "FAIL",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: false
  },
  {
    createTime: 1718161184000,
    updateTime: 1718216422000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 714,
    name: "123666",
    displayName: "w_test",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "123\n  456\n  78989"
    },
    lastCalcStatus: "FAIL",
    executeStatus: "NORMAL",
    scheduleType: "SCHEDULE",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: false
  },
  {
    createTime: 1646386655000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 602,
    name: "186456445",
    displayName: "sddfasd5864",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "TEMPORARY",
    validBeginTime: 1646323200000,
    validEndTime: 1646496000000,
    calcRule: {
      sql: "select `name` as `value`,`device_id` as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1631598909000,
    updateTime: 1668678344000,
    createUserId: 1,
    updateUserId: 1,
    createUserName: "admin",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 582,
    name: "aaa",
    displayName: "aaa",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1631588705000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 580,
    name: "aaaaa",
    displayName: "aaaaa",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1695023421000,
    updateTime: 1715327752000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 685,
    name: "aaaaaa",
    displayName: "aaaaaa",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as value,device_id as dt_id from wolf.user_sample where device_id = '4b2d6fcd5a4571a9' limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1715327753000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 100,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/指标管理/",
    whetherEnum: true,
    tagCalcTime: 1715184000000,
    busiDate: 1715184000000
  },
  {
    createTime: 1719475663000,
    updateTime: 1719475679000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 715,
    name: "age",
    displayName: "年龄",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select age as `value`,device_id as `dt_id` from `wolf`.`user_sample` where gender = '0'"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1719475676000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "INT",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1719331200000,
    busiDate: 1719331200000
  },
  {
    createTime: 1671628403000,
    updateTime: 1745735865000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 623,
    name: "angd_test_userlabel_01",
    displayName: "angd_test_userlabel_01",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as value,device_id as dt_id from wolf.user_sample where gender = '0'"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1671628603000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1682352000000
  },
  {
    createTime: 1720061873000,
    updateTime: 1720061873000,
    createUserId: 1,
    updateUserId: 1,
    createUserName: "admin",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 716,
    name: "code",
    displayName: "name",
    remark: "",
    type: "EX_SUBSCRIBE",
    subType: "EX_SUBSCRIBE",
    validDateType: "FOREVER",
    lastCalcStatus: "SUC",
    lastCalcTime: 1720061872000,
    executeStatus: "NORMAL",
    scheduleType: "SCHEDULE",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false
  },
  {
    createTime: 1720062169000,
    updateTime: 1720062169000,
    createUserId: 1,
    updateUserId: 1,
    createUserName: "admin",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 717,
    name: "code1",
    displayName: "name1",
    remark: "",
    type: "EX_SUBSCRIBE",
    subType: "EX_SUBSCRIBE",
    validDateType: "FOREVER",
    lastCalcStatus: "SUC",
    lastCalcTime: 1720062169000,
    executeStatus: "NORMAL",
    scheduleType: "SCHEDULE",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false
  },
  {
    createTime: 1720062261000,
    updateTime: 1720062261000,
    createUserId: 1,
    updateUserId: 1,
    createUserName: "admin",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 718,
    name: "code2",
    displayName: "name2",
    remark: "",
    type: "EX_SUBSCRIBE",
    subType: "EX_SUBSCRIBE",
    validDateType: "FOREVER",
    lastCalcStatus: "SUC",
    lastCalcTime: 1720062261000,
    executeStatus: "NORMAL",
    scheduleType: "SCHEDULE",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false
  },
  {
    createTime: 1721876368000,
    updateTime: 1736926744000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 719,
    name: "cs",
    displayName: "cs2",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1721876448000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1721750400000,
    busiDate: 1721750400000
  },
  {
    createTime: 1711338706000,
    updateTime: 1711338719000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 695,
    name: "date",
    displayName: "日期",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select birthday as `value`,device_id as `dt_id` from `wolf`.`user_sample` where gender = '0'"
    },
    lastCalcStatus: "SUC",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false,
    tagCalcTime: 1711209600000
  },
  {
    createTime: 1721876368000,
    updateTime: 1721876449000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 720,
    name: "label1",
    displayName: "label1",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1722960000000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1722960000000,
    busiDate: 1722960000000
  },
  {
    createTime: 1721876368000,
    updateTime: 1721876449000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 721,
    name: "label2",
    displayName: "label2",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1722960000000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1722960000000,
    busiDate: 1722960000000
  },
  {
    createTime: 1721876368000,
    updateTime: 1721876449000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 722,
    name: "label3",
    displayName: "label3",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1722960000000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1722960000000,
    busiDate: 1722960000000
  },
  {
    createTime: 1721876368000,
    updateTime: 1721876449000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 723,
    name: "label4",
    displayName: "label4",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1722960000000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1722960000000,
    busiDate: 1722960000000
  },
  {
    createTime: 1721876368000,
    updateTime: 1721876449000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 724,
    name: "label5",
    displayName: "label5",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1722960000000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1722960000000,
    busiDate: 1722960000000
  },
  {
    createTime: 1721876368000,
    updateTime: 1721876449000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 725,
    name: "label6",
    displayName: "label6",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1722960000000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1722960000000,
    busiDate: 1722960000000
  },
  {
    createTime: 1721876368000,
    updateTime: 1737018309000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 726,
    name: "label7",
    displayName: "label71",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1736909640000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1732464000000,
    busiDate: 1732464000000
  },
  {
    createTime: 1628581701000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 562,
    name: "label_0810_测试",
    displayName: "label_0810_测试",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as value,device_id as dt_id from wolf.user_sample"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1630987075000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 51,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试3/压力_测试3-1/",
    whetherEnum: true
  },
  {
    createTime: 1726121771000,
    updateTime: 1732262862000,
    createUserId: 1,
    updateUserId: 2,
    createUserName: "admin",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 730,
    name: "LBL000228",
    displayName: "低历史最大净资产区间A",
    remark: " ",
    type: "EX_SUBSCRIBE",
    subType: "EX_SUBSCRIBE",
    validDateType: "FOREVER",
    lastCalcStatus: "SUC",
    lastCalcTime: 1726120851000,
    executeStatus: "NORMAL",
    scheduleType: "SCHEDULE",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false,
    tagCalcTime: 1678809600000,
    busiDate: 1678809600000
  },
  {
    createTime: 1723630788000,
    updateTime: 1723630788000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 727,
    name: "name",
    displayName: "字符",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as `value`,device_id as `dt_id` from `wolf`.`user_sample` where scenario_code = '0'"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false
  },
  {
    createTime: 1646384338000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 601,
    name: "sdfqwe",
    displayName: "sdfsdasdf",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "TEMPORARY",
    validBeginTime: 1646323200000,
    validEndTime: 1646496000000,
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1677566083000,
    updateTime: 1682508951000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 625,
    name: "str",
    displayName: "字符",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as value, device_id as dt_id from wolf.user_sample limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1677566427000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1682352000000
  },
  {
    createTime: 1677566560000,
    updateTime: 1682508951000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 626,
    name: "str1",
    displayName: "字符1",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as value, device_id as dt_id from wolf.user_sample limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1677567179000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 11,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类10/",
    whetherEnum: true,
    tagCalcTime: 1682352000000
  },
  {
    createTime: 1646387291000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 606,
    name: "timestamp",
    displayName: "timestamp",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "TEMPORARY",
    validBeginTime: 1646323200000,
    validEndTime: 1646409600000,
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1646387417000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1732843808000,
    updateTime: 1732880309000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 731,
    name: "tttest",
    displayName: "tttest",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select ibirthday as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1732880310000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 100,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/指标管理/",
    whetherEnum: true,
    tagCalcTime: 1732723200000,
    busiDate: 1732723200000
  },
  {
    createTime: 1628581773000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 563,
    name: "user_name",
    displayName: "user_name",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1688699642000,
    updateTime: 1745735865000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 677,
    name: "七七事变标签",
    displayName: "七七事变标签",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select p1 as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1688572800000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1688572800000
  },
  {
    createTime: 1604027150000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 371,
    name: "人柱力",
    displayName: "人柱力",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 58,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/分类-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027156000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 377,
    name: "体术",
    displayName: "体术",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027139000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 366,
    name: "召唤兽",
    displayName: "召唤兽",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1645775502000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 588,
    name: "啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊",
    displayName: "啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊",
    remark: "啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select city as value, device_id as dt_id from wolf.user_sample limit 10"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 24,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/",
    whetherEnum: true
  },
  {
    createTime: 1604027148000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 368,
    name: "完成a级任务",
    displayName: "完成a级任务",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 58,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/分类-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027155000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 376,
    name: "完成b级任务",
    displayName: "完成b级任务",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027155000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 375,
    name: "完成c级任务",
    displayName: "完成c级任务",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027157000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 378,
    name: "完成s级任务",
    displayName: "完成s级任务",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027140000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 367,
    name: "属性",
    displayName: "属性",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 75,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/11/",
    whetherEnum: true
  },
  {
    createTime: 1628761226000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 564,
    name: "数值类型标签0812",
    displayName: "数值类型标签0812",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select age as value,device_id as dt_id from wolf.user_sample"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1630637693000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "INT",
    status: "NORMAL",
    categoryId: 51,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试3/压力_测试3-1/",
    whetherEnum: true
  },
  {
    createTime: 1629774322000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 565,
    name: "时间类型",
    displayName: "时间类型",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select birthday as value,device_id as dt_id from wolf.user_sample"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1629972796000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1604027153000,
    updateTime: 1719475218000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 373,
    name: "村子",
    displayName: "村子",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 100,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/指标管理/",
    whetherEnum: true
  },
  {
    createTime: 1604027154000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 374,
    name: "查克拉量",
    displayName: "查克拉量",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1630995591000,
    updateTime: 1689315522000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 575,
    name: "每天更新",
    displayName: "哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8哈哈哈8",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as value,device_id as dt_id from wolf.user_sampleABS()"
    },
    lastCalcStatus: "FAIL",
    lastCalcTime: 1688673938000,
    executeStatus: "NORMAL",
    scheduleType: "SCHEDULE",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 99,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/客户/属性/男性/",
    whetherEnum: true,
    tagCalcTime: 1682352000000
  },
  {
    createTime: 1630987493000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 574,
    name: "每天测试2",
    displayName: "每天测试2",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as value,device_id as dt_id from wolf.user_sample"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1630987511000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1606446406000,
    updateTime: 1745735918000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 392,
    name: "活跃度bool",
    displayName: "活跃度bool",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1606462840000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 398,
    name: "流失度",
    displayName: "流失度",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1687676762000,
    updateTime: 1687676794000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 676,
    name: "测试111",
    displayName: "测试111",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select p1 as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1687676780000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 13,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类2/测试三级分类/",
    whetherEnum: true,
    tagCalcTime: 1687536000000
  },
  {
    createTime: 1646375075000,
    updateTime: 1745735865000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 597,
    name: "测试时间1",
    displayName: "测试时间1",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "TEMPORARY",
    validBeginTime: 1648569600000,
    validEndTime: 1648569600000,
    calcRule: {
      sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1665540238000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 614,
    name: "测试标签",
    displayName: "测试标签01",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 87,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/新一级分类/新二级分类/",
    whetherEnum: true
  },
  {
    createTime: 1687314064000,
    updateTime: 1715667912000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 674,
    name: "测试标签同步最新时间",
    displayName: "测试标签同步最新时间显示名",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select p1 as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1715667913000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 101,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/标签管理/",
    whetherEnum: true,
    tagCalcTime: 1715529600000,
    busiDate: 1715529600000
  },
  {
    createTime: 1687675978000,
    updateTime: 1687676793000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 675,
    name: "测试标签最近同步时间",
    displayName: "测试标签最近同步时间",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select p1 as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1687676226000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 13,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试一级分类/测试二级分类2/测试三级分类/",
    whetherEnum: true,
    tagCalcTime: 1687536000000
  },
  {
    createTime: 1604027149000,
    updateTime: 1719474365000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 369,
    name: "级别",
    displayName: "级别",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 101,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/标签管理/",
    whetherEnum: true
  },
  {
    createTime: 1648624368000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 610,
    name: "自动",
    displayName: "自动",
    remark: " ",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "FOREVER",
    calcRule: {
      sql: "select name as value,device_id as dt_id from wolf.user_sample"
    },
    lastCalcStatus: "SUC",
    lastCalcTime: 1648624375000,
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1608779375000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 414,
    name: "自动标签_LONG_2020-12-24",
    displayName: "自动标签_LONG_2020-12-24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 23,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/",
    whetherEnum: true
  },
  {
    createTime: 1608864948000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 417,
    name: "自动标签_LONG_2020-12-25",
    displayName: "自动标签_LONG_2020-12-25",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1610086419000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 421,
    name: "自动标签_LONG_2021-01-08",
    displayName: "自动标签_LONG_2021-01-08",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1611309828000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 433,
    name: "自动标签_LONG_2021-01-22",
    displayName: "自动标签_LONG_2021-01-22",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1611641132000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 438,
    name: "自动标签_LONG_2021-01-26",
    displayName: "自动标签_LONG_2021-01-26",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1612322151000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 439,
    name: "自动标签_LONG_2021-02-03",
    displayName: "自动标签_LONG_2021-02-03",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1625733563000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 543,
    name: "自动标签_LONG_2021_07_08",
    displayName: "自动标签_LONG_2021_07_08",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1627009377000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 548,
    name: "自动标签_LONG_2021_07_23",
    displayName: "自动标签_LONG_2021_07_23",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1627890209000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 558,
    name: "自动标签_LONG_2021_08_02",
    displayName: "自动标签_LONG_2021_08_02",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1628567546000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 559,
    name: "自动标签_LONG_2021_08_10",
    displayName: "自动标签_LONG_2021_08_10",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1629774640000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 568,
    name: "自动标签_LONG_2021_08_24",
    displayName: "自动标签_LONG_2021_08_24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1630553053000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 569,
    name: "自动标签_LONG_2021_09_02",
    displayName: "自动标签_LONG_2021_09_02",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1631588755000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 581,
    name: "自动标签_LONG_2021_09_14",
    displayName: "自动标签_LONG_2021_09_14",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1644897081000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 585,
    name: "自动标签_LONG_2022_02_15",
    displayName: "自动标签_LONG_2022_02_15",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1670901160000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 617,
    name: "自动标签_LONG_2022_12_13",
    displayName: "哈哈哈11",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 98,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/用户/标签/活跃/",
    whetherEnum: true
  },
  {
    createTime: 1709016726000,
    updateTime: 1709017474000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 689,
    name: "自动标签_LONG_2024_02_27",
    displayName: "自动标签_LONG_2024_02_27",
    remark: "",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcTime: 1709017583000,
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1708876800000
  },
  {
    createTime: 1710832578000,
    updateTime: 1710832587000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 692,
    name: "自动标签_LONG_2024_03_19",
    displayName: "自动标签_LONG_2024_03_19",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "LONG",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false,
    tagCalcTime: 1710691200000
  },
  {
    createTime: 1732880309000,
    updateTime: 1732880313000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 734,
    name: "自动标签_LONG_2024_11_29",
    displayName: "自动标签_LONG_2024_11_29",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcTime: 1732880310000,
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1732723200000,
    busiDate: 1732723200000
  },
  {
    createTime: 1608277949000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 405,
    name: "自动标签_STRING_2020-12-18",
    displayName: "自动标签_STRING_2020-12-18",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1608779375000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 413,
    name: "自动标签_STRING_2020-12-24",
    displayName: "自动标签_STRING_2020-12-24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1608864947000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 416,
    name: "自动标签_STRING_2020-12-25",
    displayName: "自动标签_STRING_2020-12-25",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1610086417000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 420,
    name: "自动标签_STRING_2021-01-08",
    displayName: "自动标签_STRING_2021-01-08",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1611309829000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 435,
    name: "自动标签_STRING_2021-01-22",
    displayName: "自动标签_STRING_2021-01-22",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1611641132000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 437,
    name: "自动标签_STRING_2021-01-26",
    displayName: "自动标签_STRING_2021-01-26",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1614160891000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 444,
    name: "自动标签_STRING_2021-02-24",
    displayName: "自动标签_STRING_2021-02-24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1625733563000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 545,
    name: "自动标签_STRING_2021_07_08",
    displayName: "自动标签_STRING_2021_07_08",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1627009376000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 546,
    name: "自动标签_STRING_2021_07_23",
    displayName: "自动标签_STRING_2021_07_23",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1627890208000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 556,
    name: "自动标签_STRING_2021_08_02",
    displayName: "自动标签_STRING_2021_08_02",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1628567561000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 560,
    name: "自动标签_STRING_2021_08_10",
    displayName: "自动标签_STRING_2021_08_10",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1629774605000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 566,
    name: "自动标签_STRING_2021_08_24",
    displayName: "自动标签_STRING_2021_08_24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1630553054000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 570,
    name: "自动标签_STRING_2021_09_02",
    displayName: "自动标签_STRING_2021_09_02",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1631588704000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 578,
    name: "自动标签_STRING_2021_09_14",
    displayName: "自动标签_STRING_2021_09_14",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1644897081000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 584,
    name: "自动标签_STRING_2022_02_15",
    displayName: "自动标签_STRING_2022_02_15",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1670901160000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 616,
    name: "自动标签_STRING_2022_12_13",
    displayName: "自动标签_STRING_2022_12_13",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1709016727000,
    updateTime: 1709017475000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 690,
    name: "自动标签_STRING_2024_02_27",
    displayName: "自动标签_STRING_2024_02_27",
    remark: "",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcTime: 1709017583000,
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1708876800000
  },
  {
    createTime: 1710832578000,
    updateTime: 1710832597000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 694,
    name: "自动标签_STRING_2024_03_19",
    displayName: "自动标签_STRING_2024_03_19",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false,
    tagCalcTime: 1710691200000
  },
  {
    createTime: 1732880309000,
    updateTime: 1732880311000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 733,
    name: "自动标签_STRING_2024_11_29",
    displayName: "自动标签_STRING_2024_11_29",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcTime: 1732880310000,
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1732723200000,
    busiDate: 1732723200000
  },
  {
    createTime: 1608277949000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 406,
    name: "自动标签_TIMESTAMP_2020-12-18",
    displayName: "自动标签_TIMESTAMP_2020-12-18",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 23,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/",
    whetherEnum: true
  },
  {
    createTime: 1608779375000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 415,
    name: "自动标签_TIMESTAMP_2020-12-24",
    displayName: "自动标签_TIMESTAMP_2020-12-24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 23,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/",
    whetherEnum: true
  },
  {
    createTime: 1608864949000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 418,
    name: "自动标签_TIMESTAMP_2020-12-25",
    displayName: "自动标签_TIMESTAMP_2020-12-25",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1610086416000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 419,
    name: "自动标签_TIMESTAMP_2021-01-08",
    displayName: "自动标签_TIMESTAMP_2021-01-08",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1611309828000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 434,
    name: "自动标签_TIMESTAMP_2021-01-22",
    displayName: "自动标签_TIMESTAMP_2021-01-22",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1611641131000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 436,
    name: "自动标签_TIMESTAMP_2021-01-26",
    displayName: "自动标签_TIMESTAMP_2021-01-26",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1612322152000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 440,
    name: "自动标签_TIMESTAMP_2021-02-03",
    displayName: "自动标签_TIMESTAMP_2021-02-03",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 61,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/测试/",
    whetherEnum: true
  },
  {
    createTime: 1625733563000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 544,
    name: "自动标签_TIMESTAMP_2021_07_08",
    displayName: "自动标签_TIMESTAMP_2021_07_08",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1627009376000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 547,
    name: "自动标签_TIMESTAMP_2021_07_23",
    displayName: "自动标签_TIMESTAMP_2021_07_23",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1627890208000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 557,
    name: "自动标签_TIMESTAMP_2021_08_02",
    displayName: "自动标签_TIMESTAMP_2021_08_02",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1628567566000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 561,
    name: "自动标签_TIMESTAMP_2021_08_10",
    displayName: "自动标签_TIMESTAMP_2021_08_10",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1629774640000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 567,
    name: "自动标签_TIMESTAMP_2021_08_24",
    displayName: "自动标签_TIMESTAMP_2021_08_24",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1630553054000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 571,
    name: "自动标签_TIMESTAMP_2021_09_02",
    displayName: "自动标签_TIMESTAMP_2021_09_02",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1631588705000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 579,
    name: "自动标签_TIMESTAMP_2021_09_14",
    displayName: "自动标签_TIMESTAMP_2021_09_14",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1644897080000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 583,
    name: "自动标签_TIMESTAMP_2022_02_15",
    displayName: "自动标签_TIMESTAMP_2022_02_15",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 70,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/wdd/",
    whetherEnum: true
  },
  {
    createTime: 1670901160000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 618,
    name: "自动标签_TIMESTAMP_2022_12_13",
    displayName: "哈哈哈",
    remark: "",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 96,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/客户/渠道/",
    whetherEnum: true
  },
  {
    createTime: 1709016727000,
    updateTime: 1709017475000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 691,
    name: "自动标签_TIMESTAMP_2024_02_27",
    displayName: "自动标签_TIMESTAMP_2024_02_27",
    remark: "",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcTime: 1709017583000,
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1708876800000
  },
  {
    createTime: 1710832578000,
    updateTime: 1710832593000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 693,
    name: "自动标签_TIMESTAMP_2024_03_19",
    displayName: "自动标签_TIMESTAMP_2024_03_19",
    remark: " ",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    dataType: "TIMESTAMP",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: false,
    tagCalcTime: 1710691200000
  },
  {
    createTime: 1732880309000,
    updateTime: 1732880310000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 732,
    name: "自动标签_TIMESTAMP_2024_11_29",
    displayName: "自动标签_TIMESTAMP_2024_11_29",
    type: "EXTERNAL_IMPORT",
    subType: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    lastCalcTime: 1732880310000,
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true,
    tagCalcTime: 1732723200000,
    busiDate: 1732723200000
  },
  {
    createTime: 1604027149000,
    updateTime: 1741857123000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 370,
    name: "血继极限",
    displayName: "血继极限 ",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  },
  {
    createTime: 1646297803000,
    updateTime: 1668671601000,
    createUserId: 2,
    updateUserId: 2,
    createUserName: "analyzer",
    updateUserName: "analyzer",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 590,
    name: "阿斯顿倒萨",
    displayName: "阿德撒旦",
    remark: "",
    type: "SQL_CREATION",
    subType: "CUSTOM_SQL",
    validDateType: "TEMPORARY",
    validBeginTime: 1646236800000,
    validEndTime: 1646323200000,
    calcRule: {
      sql: "select d as value,id as dt_id from wolf.test_user_date"
    },
    lastCalcStatus: "NOTRUN",
    executeStatus: "NORMAL",
    scheduleType: "MANUAL",
    scheduleRate: "DAY",
    scheduleConf: {
      days: 1,
      weekTerm: "MONDAY"
    },
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 0,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    whetherEnum: true
  },
  {
    createTime: 1604027152000,
    updateTime: 1672712208000,
    createUserId: 2,
    updateUserId: 1,
    createUserName: "analyzer",
    updateUserName: "admin",
    projectId: "qvAD1jk8q0hA0Oxm",
    id: 372,
    name: "饭量",
    displayName: "饭量",
    type: "EXTERNAL_IMPORT",
    validDateType: "FOREVER",
    dataType: "STRING",
    status: "NORMAL",
    categoryId: 46,
    scenario: {
      createTime: ***********00,
      updateTime: 1733898657000,
      createUserId: 1,
      updateUserId: 2,
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 14,
      businessEntityCode: "entity_user",
      name: "女性场景",
      code: "0",
      joinEventColumnName: "userId",
      joinOrderColumnName: "user_id",
      isDefault: true,
      orderNum: 2
    },
    labelCategories: "/压力_测试1/压力_测试2/压力测试2-1/",
    whetherEnum: true
  }
];
const findAllCategory = (data: any) => {
  return {
    categoryList: [
      {
        createTime: 1600236868000,
        updateTime: 1600337856000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 10,
        name: "测试一级分类",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1600412332000,
        updateTime: 1600413923000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 23,
        name: "压力_测试1",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1603097299000,
        updateTime: 1603954868000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 58,
        name: "分类-1",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1603097310000,
        updateTime: 1603954859000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 59,
        name: "分类-0",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1610086513000,
        updateTime: 1610086513000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 61,
        name: "测试",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1619417716000,
        updateTime: 1619417716000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 62,
        name: "name",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,",
        remark: "dd"
      },
      {
        createTime: 1619417994000,
        updateTime: 1619417994000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 64,
        name: "name222",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,",
        remark: "nsm"
      },
      {
        createTime: 1620877267000,
        updateTime: 1621497137000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 70,
        name: "wdd",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1650960878000,
        updateTime: 1650960878000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 75,
        name: "11",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,",
        remark: "11"
      },
      {
        createTime: 1665468464000,
        updateTime: 1665468464000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 86,
        name: "新一级分类",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1670902981000,
        updateTime: 1670902981000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 92,
        name: "客户",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1670902981000,
        updateTime: 1670902981000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 93,
        name: "用户",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1682505218000,
        updateTime: 1682506676000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 100,
        name: "指标管理",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      },
      {
        createTime: 1682507278000,
        updateTime: 1682507278000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 101,
        name: "标签管理",
        type: "FIRST_LEVEL",
        parentId: 0,
        path: "0,"
      }
    ],
    userLabels: [
      {
        createTime: 1606462840000,
        updateTime: 1672712208000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 398,
        name: "流失度",
        displayName: "流失度",
        type: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1631588704000,
        updateTime: 1672712208000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 578,
        name: "自动标签_STRING_2021_09_14",
        displayName: "自动标签_STRING_2021_09_14",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1631588705000,
        updateTime: 1672712208000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 579,
        name: "自动标签_TIMESTAMP_2021_09_14",
        displayName: "自动标签_TIMESTAMP_2021_09_14",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1631588705000,
        updateTime: 1672712208000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 580,
        name: "aaaaa",
        displayName: "aaaaa",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1631588755000,
        updateTime: 1672712208000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 581,
        name: "自动标签_LONG_2021_09_14",
        displayName: "自动标签_LONG_2021_09_14",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1631598909000,
        updateTime: 1668678344000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 582,
        name: "aaa",
        displayName: "aaa",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1645775054000,
        updateTime: 1668671601000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 587,
        name: "1",
        displayName: "1",
        remark: "顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶顶",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select city as value, device_id as dt_id from wolf.user_sample limit 10"
        },
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "DOUBLE",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1646297803000,
        updateTime: 1668671601000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 590,
        name: "阿斯顿倒萨",
        displayName: "阿德撒旦",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "TEMPORARY",
        validBeginTime: 1646236800000,
        validEndTime: 1646323200000,
        calcRule: {
          sql: "select d as value,id as dt_id from wolf.test_user_date"
        },
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1646375075000,
        updateTime: 1745735865000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 597,
        name: "测试时间1",
        displayName: "测试时间1",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "TEMPORARY",
        validBeginTime: 1648569600000,
        validEndTime: 1648569600000,
        calcRule: {
          sql: "select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"
        },
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "LONG",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1646386655000,
        updateTime: 1668671601000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 602,
        name: "186456445",
        displayName: "sddfasd5864",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "TEMPORARY",
        validBeginTime: 1646323200000,
        validEndTime: 1646496000000,
        calcRule: {
          sql: "select `name` as `value`,`device_id` as `dt_id` from `wolf`.`user_sample` limit 10"
        },
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1648624368000,
        updateTime: 1668671601000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 610,
        name: "自动",
        displayName: "自动",
        remark: " ",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select name as value,device_id as dt_id from wolf.user_sample"
        },
        lastCalcStatus: "SUC",
        lastCalcTime: 1648624375000,
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1670901160000,
        updateTime: 1672712208000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 616,
        name: "自动标签_STRING_2022_12_13",
        displayName: "自动标签_STRING_2022_12_13",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true
      },
      {
        createTime: 1671628403000,
        updateTime: 1745735865000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 623,
        name: "angd_test_userlabel_01",
        displayName: "angd_test_userlabel_01",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select name as value,device_id as dt_id from wolf.user_sample where gender = '0'"
        },
        lastCalcStatus: "SUC",
        lastCalcTime: 1671628603000,
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1682352000000
      },
      {
        createTime: 1688699642000,
        updateTime: 1745735865000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 677,
        name: "七七事变标签",
        displayName: "七七事变标签",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select p1 as value,id as dt_id from wolf.test_user_date"
        },
        lastCalcStatus: "SUC",
        lastCalcTime: 1688572800000,
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1688572800000
      },
      {
        createTime: 1709016726000,
        updateTime: 1709017474000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 689,
        name: "自动标签_LONG_2024_02_27",
        displayName: "自动标签_LONG_2024_02_27",
        remark: "",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcTime: 1709017583000,
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1708876800000
      },
      {
        createTime: 1709016727000,
        updateTime: 1709017475000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 690,
        name: "自动标签_STRING_2024_02_27",
        displayName: "自动标签_STRING_2024_02_27",
        remark: "",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcTime: 1709017583000,
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1708876800000
      },
      {
        createTime: 1709016727000,
        updateTime: 1709017475000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 691,
        name: "自动标签_TIMESTAMP_2024_02_27",
        displayName: "自动标签_TIMESTAMP_2024_02_27",
        remark: "",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcTime: 1709017583000,
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1708876800000
      },
      {
        createTime: 1710832578000,
        updateTime: 1710832587000,
        createUserId: 2,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 692,
        name: "自动标签_LONG_2024_03_19",
        displayName: "自动标签_LONG_2024_03_19",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        dataType: "LONG",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false,
        tagCalcTime: 1710691200000
      },
      {
        createTime: 1710832578000,
        updateTime: 1710832593000,
        createUserId: 2,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 693,
        name: "自动标签_TIMESTAMP_2024_03_19",
        displayName: "自动标签_TIMESTAMP_2024_03_19",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        dataType: "TIMESTAMP",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false,
        tagCalcTime: 1710691200000
      },
      {
        createTime: 1710832578000,
        updateTime: 1710832597000,
        createUserId: 2,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 694,
        name: "自动标签_STRING_2024_03_19",
        displayName: "自动标签_STRING_2024_03_19",
        remark: " ",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false,
        tagCalcTime: 1710691200000
      },
      {
        createTime: 1711338706000,
        updateTime: 1711338719000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 695,
        name: "date",
        displayName: "日期",
        remark: " ",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select birthday as `value`,device_id as `dt_id` from `wolf`.`user_sample` where gender = '0'"
        },
        lastCalcStatus: "SUC",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "TIMESTAMP",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false,
        tagCalcTime: 1711209600000
      },
      {
        createTime: 1719475663000,
        updateTime: 1719475679000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 715,
        name: "age",
        displayName: "年龄",
        remark: " ",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select age as `value`,device_id as `dt_id` from `wolf`.`user_sample` where gender = '0'"
        },
        lastCalcStatus: "SUC",
        lastCalcTime: 1719475676000,
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "INT",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1719331200000,
        busiDate: 1719331200000
      },
      {
        createTime: 1720061873000,
        updateTime: 1720061873000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 716,
        name: "code",
        displayName: "name",
        remark: "",
        type: "EX_SUBSCRIBE",
        subType: "EX_SUBSCRIBE",
        validDateType: "FOREVER",
        lastCalcStatus: "SUC",
        lastCalcTime: 1720061872000,
        executeStatus: "NORMAL",
        scheduleType: "SCHEDULE",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false
      },
      {
        createTime: 1720062169000,
        updateTime: 1720062169000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 717,
        name: "code1",
        displayName: "name1",
        remark: "",
        type: "EX_SUBSCRIBE",
        subType: "EX_SUBSCRIBE",
        validDateType: "FOREVER",
        lastCalcStatus: "SUC",
        lastCalcTime: 1720062169000,
        executeStatus: "NORMAL",
        scheduleType: "SCHEDULE",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false
      },
      {
        createTime: 1720062261000,
        updateTime: 1720062261000,
        createUserId: 1,
        updateUserId: 1,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 718,
        name: "code2",
        displayName: "name2",
        remark: "",
        type: "EX_SUBSCRIBE",
        subType: "EX_SUBSCRIBE",
        validDateType: "FOREVER",
        lastCalcStatus: "SUC",
        lastCalcTime: 1720062261000,
        executeStatus: "NORMAL",
        scheduleType: "SCHEDULE",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false
      },
      {
        createTime: 1723630788000,
        updateTime: 1723630788000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 727,
        name: "name",
        displayName: "字符",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select name as `value`,device_id as `dt_id` from `wolf`.`user_sample` where scenario_code = '0'"
        },
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false
      },
      {
        createTime: 1723631723000,
        updateTime: 1723631723000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 728,
        name: "0814",
        displayName: "姓名",
        remark: "",
        type: "SQL_CREATION",
        subType: "CUSTOM_SQL",
        validDateType: "FOREVER",
        calcRule: {
          sql: "select name as `value`,device_id as `dt_id` from `wolf`.`user_sample` where gender = '0'"
        },
        lastCalcStatus: "NOTRUN",
        executeStatus: "NORMAL",
        scheduleType: "MANUAL",
        scheduleRate: "DAY",
        scheduleConf: {
          days: 1,
          weekTerm: "MONDAY"
        },
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false
      },
      {
        createTime: 1726121771000,
        updateTime: 1732262862000,
        createUserId: 1,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 730,
        name: "LBL000228",
        displayName: "低历史最大净资产区间A",
        remark: " ",
        type: "EX_SUBSCRIBE",
        subType: "EX_SUBSCRIBE",
        validDateType: "FOREVER",
        lastCalcStatus: "SUC",
        lastCalcTime: 1726120851000,
        executeStatus: "NORMAL",
        scheduleType: "SCHEDULE",
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: false,
        tagCalcTime: 1678809600000,
        busiDate: 1678809600000
      },
      {
        createTime: 1732880309000,
        updateTime: 1732880310000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 732,
        name: "自动标签_TIMESTAMP_2024_11_29",
        displayName: "自动标签_TIMESTAMP_2024_11_29",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcTime: 1732880310000,
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1732723200000,
        busiDate: 1732723200000
      },
      {
        createTime: 1732880309000,
        updateTime: 1732880311000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 733,
        name: "自动标签_STRING_2024_11_29",
        displayName: "自动标签_STRING_2024_11_29",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcTime: 1732880310000,
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1732723200000,
        busiDate: 1732723200000
      },
      {
        createTime: 1732880309000,
        updateTime: 1732880313000,
        createUserId: 2,
        updateUserId: 2,
        projectId: "qvAD1jk8q0hA0Oxm",
        id: 734,
        name: "自动标签_LONG_2024_11_29",
        displayName: "自动标签_LONG_2024_11_29",
        type: "EXTERNAL_IMPORT",
        subType: "EXTERNAL_IMPORT",
        validDateType: "FOREVER",
        lastCalcTime: 1732880310000,
        dataType: "STRING",
        status: "NORMAL",
        categoryId: 0,
        scenario: {
          createTime: ***********00,
          updateTime: 1733898657000,
          createUserId: 1,
          updateUserId: 2,
          projectId: "qvAD1jk8q0hA0Oxm",
          id: 14,
          businessEntityCode: "entity_user",
          name: "女性场景",
          code: "0",
          joinEventColumnName: "userId",
          joinOrderColumnName: "user_id",
          isDefault: true,
          orderNum: 2
        },
        whetherEnum: true,
        tagCalcTime: 1732723200000,
        busiDate: 1732723200000
      }
    ]
  };
};

const findAllByLabelId = () => [];

/** hover提示的标签 */
const getTagValuesById = (id) => [
  {
    createTime: 1631598935000,
    updateTime: 1727075201000,
    createUserId: 1,
    updateUserId: 2,
    id: 17891,
    labelId: 582,
    value: "1",
    displayValue: "北京",
    priorityShow: 2,
    valueAndDisplayValue: "[1]北京"
  },
  {
    createTime: 1631598944000,
    updateTime: 1727075205000,
    createUserId: 1,
    updateUserId: 2,
    id: 17892,
    labelId: 582,
    value: "2",
    displayValue: "上海",
    priorityShow: 2,
    valueAndDisplayValue: "[2]上海"
  },
  {
    createTime: 1631598953000,
    updateTime: 1727075210000,
    createUserId: 1,
    updateUserId: 2,
    id: 17893,
    labelId: 582,
    value: "3",
    displayValue: "南京",
    priorityShow: 2,
    valueAndDisplayValue: "[3]南京"
  }
];

const labelMockDataProvider = {
  // 模拟获取标签列表
  getTagList: async (data: any) => {
    return getTagList(data);
  },

  // 模拟获取分类列表
  findCategoryByProjectId: async () => {
    return findCategoryByProjectId({});
  },

  // 模拟获取所有分类
  findAllCategory: async (data: any) => {
    return findAllCategory(data);
  },

  // 模拟获取标签值
  getTagValuesById: async (id: number) => {
    return getTagValuesById({
      labelId: id
    });
  }
};

export { labelMockDataProvider };
