<template>
  <div class="test-label-page">
    <h1>Label Filter 测试页面</h1>
    <div class="test-section">
      <h3>控制面板:</h3>
      <a-button @click="toggleMode"> 切换模式 (当前: {{ mode }}) </a-button>
      <a-button @click="clearFilter" style="margin-left: 8px"> 清空过滤器 </a-button>
      <a-button @click="validateFilter" style="margin-left: 8px"> 验证过滤器 </a-button>
    </div>

    <div class="flex">
      <div class="w-[85%]">
        <div class="test-section">
          <h2>Vue2 版本的 Label Filter</h2>
          <LabelFilter
            ref="labelFilterRef"
            :value="filterValue"
            :dataProvider="mockDataProvider"
            :onChange="handleFilterChange"
            :mode="mode"
            :showInitLine="true"
            :checked="false"
            :campaignInfo="{}"
            :isUserGroup="false"
            :isCampaignV2="false"
          />
        </div>

        <div class="test-section">
          <h3>当前过滤器值:</h3>
          <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre>
        </div>
      </div>

      <div class="w-[15%]">
        <!-- 右侧JSON编辑区域 -->
        <div class="json-editor">
          <a-button @click="submitJsonData" style="margin-bottom: 10px"> 应用JSON数据 </a-button>
          <a-button @click="setDemoValue" style="margin-bottom: 10px"> 设置demo数据 </a-button>
          <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LabelFilter from "@/components/label/Filter.vue";
import FilterModelUtil from "@/components/label/FilterModelUtil";
import { labelMockDataProvider } from "./testLabelConfig";

// 模拟demo数据
const demoValue = {
  connector: "AND",
  filters: [
    {
      connector: "AND",
      filters: [
        {
          id: "1",
          label: "age",
          displayName: "年龄",
          operator: "GT",
          value: 18,
          fieldType: "INT",
          dateType: "RELATIVE",
          times: 0,
          showValue: "18",
          checkUserTag: false,
          checkDuration: 1,
          timeTerm: "HOUR",
          timeType: "DAY",
          exCalendar: {},
          tagInfo: {
            id: "1",
            name: "age",
            displayName: "年龄",
            dataType: "INT",
            busiDate: false,
            userLabelValues: [
              { value: "18", displayValue: "18岁" },
              { value: "25", displayValue: "25岁" },
              { value: "30", displayValue: "30岁" }
            ]
          }
        }
      ]
    }
  ]
};

export default {
  name: "TestLabel",
  components: {
    LabelFilter
  },
  data() {
    return {
      jsonData: "{}",
      filterValue: {},
      mode: "edit",
      mockDataProvider: labelMockDataProvider
    };
  },
  watch: {
    filterValue: {
      handler(newVal) {
        console.log("filterValue changed:", newVal);
        this.jsonData = JSON.stringify(newVal, null, 2);
      },
      deep: true
    }
  },
  methods: {
    handleFilterChange(validJson, fullValue) {
      console.log("Filter changed:", validJson, fullValue);
      this.filterValue = validJson || {};
    },

    toggleMode() {
      this.mode = this.mode === "edit" ? "detail" : "edit";
    },

    clearFilter() {
      this.filterValue = {};
    },

    validateFilter() {
      if (this.$refs.labelFilterRef) {
        const isValid = this.$refs.labelFilterRef.isValid();
        console.log("isValid:", isValid);
        this.$message[isValid ? "success" : "error"](`验证结果: ${isValid ? "通过" : "失败"}`);
      }
    },

    submitJsonData() {
      try {
        this.filterValue = JSON.parse(this.jsonData);
      } catch (error) {
        this.$message.error("JSON格式错误: " + error.message);
      }
    },

    setDemoValue() {
      this.filterValue = demoValue;
    }
  },
  mounted() {
    // 初始化一个空的过滤器
    this.filterValue = FilterModelUtil.initCreateFilterGroup(true);

    window.setLabelValue = this.setDemoValue;
  }
};
</script>

<style scoped>
.test-label-page {
  padding: 20px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h2,
.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.json-editor {
  padding: 10px;
}

.flex {
  display: flex;
  gap: 20px;
}

.w-\[85\%\] {
  width: 85%;
}

.w-\[15\%\] {
  width: 15%;
}
</style>
