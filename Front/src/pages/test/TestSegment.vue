<template>
  <div>
    <h1>TestSegment - 分群组件测试</h1>

    <div style="margin: 20px 0">
      <h2>分群过滤器组件测试</h2>
      <div style="margin: 20px; width: 1360px; padding: 20px; border: 1px solid #000; display: inline-block">
        <SegmentFilter
          ref="segmentFilterRef"
          :dataProvider="dataProvider"
          :value="filterValue"
          :onChange="handleFilterChange"
          :mode="mode"
          :showInitLine="true"
        />
        <a-button @click="clearValue">清空</a-button>
        <a-button @click="toggleMode">
          {{ mode === "edit" ? "切换到只读" : "切换到编辑" }}
        </a-button>
        <a-button @click="onSubmit">提交验证</a-button>
        <hr />
        <div>
          <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre>
        </div>
      </div>
      <div style="display: inline-block; width: 500px">
        <a-button @click="submitJsonData">提交JSON数据</a-button>
        <a-textarea v-model="jsonData" :rows="30" @change="handleJsonChange" />
      </div>
    </div>

    <div style="margin: 20px 0">
      <h3>可用分群列表:</h3>
      <ul>
        <li v-for="segment in segmentList" :key="segment.id">
          [{{ segment.id }}] {{ segment.name }} - {{ segment.customerCount }}人
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { Button, Input } from "ant-design-vue";
import SegmentFilter from "../../components/segment/Filter.vue";

const filterInfo = {
  connector: "AND",
  filters: [
    {
      connector: "AND",
      filters: [
        {
          type: "INCLUDE",
          segment: {
            id: 915,
            name: "营销高响应用户",
            lastCalcTime: 1617181260000,
            customerCount: 1
          }
        }
      ]
    },
    {
      connector: "AND",
      filters: [
        {
          type: "INCLUDE",
          segment: {
            id: 916,
            name: "高价值客户",
            lastCalcTime: 1617181440000,
            customerCount: 7742
          }
        }
      ]
    }
  ]
};

const datas = {
  header: { code: 0 },
  body: [
    {
      createTime: 1617002339000,
      updateTime: 1630656216000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: "analyzer",
      updateUserName: "analyzer",
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 873,
      name: "理财购买高响应用户",
      customerCount: 265,
      status: "NORMAL",
      calcStatus: "SUC",
      calcMemo: "",
      type: "CONDITIONAL",
      lastCalcTime: 1630656180000,
      calcRule: "ONCE",
      scheduleConf: { calcRule: "ONCE" },
      connector: "AND",
      whetherTest: true
    },
    {
      createTime: 1617181139000,
      updateTime: 1617181279000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: "analyzer",
      updateUserName: "analyzer",
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 915,
      name: "营销高响应用户",
      customerCount: 1,
      status: "NORMAL",
      calcStatus: "SUC",
      calcMemo: "",
      type: "UPLOAD",
      lastCalcTime: 1617181260000,
      calcRule: "ONCE",
      scheduleConf: { calcRule: "ONCE" },
      display: true,
      uploadPath: "tmp/185391af-039e-4bc4-b089-29f4466b1951.csv",
      whetherTest: true
    },
    {
      createTime: 1617181159000,
      updateTime: 1617181459000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: "analyzer",
      updateUserName: "analyzer",
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 916,
      name: "高价值客户",
      customerCount: 7742,
      status: "NORMAL",
      calcStatus: "SUC",
      calcMemo: "",
      type: "COMPLEX",
      lastCalcTime: 1617181440000,
      calcRule: "ONCE",
      scheduleConf: { calcRule: "ONCE" },
      display: true,
      limits: 0,
      whetherTest: true
    },
    {
      createTime: 1617180801000,
      updateTime: 1617180913000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: "analyzer",
      updateUserName: "analyzer",
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 914,
      name: "贷款高意愿客户",
      customerCount: 7742,
      status: "NORMAL",
      calcStatus: "SUC",
      calcMemo: "",
      type: "CONDITIONAL",
      lastCalcTime: 1617180900000,
      calcRule: "ONCE",
      scheduleConf: { calcRule: "ONCE" },
      connector: "AND",
      display: true,
      whetherTest: true
    },
    {
      createTime: 1626660355000,
      updateTime: 1626660545000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: "analyzer",
      updateUserName: "analyzer",
      projectId: "qvAD1jk8q0hA0Oxm",
      id: 1064,
      name: "活动敏感用户",
      customerCount: 265,
      status: "NORMAL",
      calcStatus: "SUC",
      calcMemo: "",
      type: "CONDITIONAL",
      lastCalcTime: 1626660540000,
      calcRule: "ONCE",
      scheduleConf: { calcRule: "ONCE" },
      connector: "AND",
      display: true,
      whetherTest: true
    }
  ]
};

export default {
  name: "TestSegment",
  components: {
    SegmentFilter,
    "a-button": Button,
    "a-textarea": Input.TextArea
  },
  data() {
    return {
      filterValue: filterInfo,
      mode: "edit",
      jsonData: "{}",
      segmentList: datas.body,
      dataProvider: {
        getSegmentList: (searchText) => {
          // 模拟搜索功能
          if (!searchText) {
            return Promise.resolve(datas.body);
          }
          const filtered = datas.body.filter((item) => item.name.toLowerCase().includes(searchText.toLowerCase()));
          return Promise.resolve(filtered);
        },
        getGroupList: () => {
          return Promise.resolve(datas.body);
        }
      }
    };
  },
  watch: {
    filterValue: {
      handler(newValue) {
        this.jsonData = JSON.stringify(newValue, null, 2);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleFilterChange(newValue, rawValue) {
      console.log("Filter changed:", newValue, rawValue);
      this.filterValue = newValue;
    },

    clearValue() {
      this.filterValue = {};
    },

    toggleMode() {
      this.mode = this.mode === "edit" ? "detail" : "edit";
    },

    onSubmit() {
      if (this.$refs.segmentFilterRef) {
        const isValid = this.$refs.segmentFilterRef.isValid();
        console.log("isValid:", isValid);
        alert(`验证结果: ${isValid ? "通过" : "失败"}`);
      }
    },

    submitJsonData() {
      try {
        this.filterValue = JSON.parse(this.jsonData);
      } catch (error) {
        alert("JSON格式错误: " + error.message);
      }
    },

    handleJsonChange(e) {
      this.jsonData = e.target.value;
    }
  }
};
</script>

<style>
pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
