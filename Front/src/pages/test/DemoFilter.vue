<template>
  <div>
    <!-- 6.22用isActionCollection这个参数控制时间选择器 -->
    <ActionCollective
      ref="actionCollectiveRef"
      :value="filterValue"
      :dataProvider="mockDataProvider"
      :onChange="handleFilterChange"
      :mode="mode"
      :showInitLine="true"
      :isActionCollection="hourOrMinute"
      :isUserGroup="false"
    />
  </div>
</template>

<script>
import {
  tableId,
  propertyItemList,
  propertyList,
  eventDatas,
  functions,
  eventProperty,
  demoValue,
  mockDataProvider,
  demoValueMinuteOrHour
} from "@/pages/test/TestEventConfig";
import ActionCollective from "@/components/actioncollective/actioncollective.vue";

export default {
  name: "DemoFilter",
  components: {
    ActionCollective
  },
  props: {
    mode: {
      type: String,
      default: "edit"
    },
    hourOrMinute: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterValue: this.hourOrMinute ? demoValueMinuteOrHour : demoValue,
      mockDataProvider
    };
  },
  methods: {
    handleFilterChange(validJson, fullValue) {
      console.log("Filter changed:", validJson, fullValue);
      this.filterValue = validJson || {};
    }
  }
};
</script>

<style></style>
