<template>
  <div class="">
    <div>
      <CPNTFilter
        :dataProvider="dataProvider"
        :value="value"
        :onChange="onChange"
        :mode="mode"
        addButtonText="添加过滤条件"
      />
      <a-button @click="toggleMode">只读切换</a-button>
    </div>

    <div class="h-full">
      <json-viewer :value="value" :expand-depth="5" sort></json-viewer>
    </div>
  </div>
</template>

<script>
import _ from "lodash";
import { CPNTFilter, CPNTEvent } from "@/components/index";

const tableId = 1;
const propertyItemList = [
  {
    tableId,
    schemaId: 1,
    items: ["北京", "上海", "广州"]
  },
  {
    tableId,
    schemaId: 2,
    items: [
      { name: "页面浏览", value: "pageview" },
      { name: "登录", value: "login" },
      { name: "注册", value: "register" },
      { name: "点击", value: "click" },
      { name: "购买", value: "pay" },
      { name: "下单", value: "order" }
    ]
  },
  {
    tableId,
    schemaId: 4,
    items: [20, 80, 40]
  }
];
const propertyList = [
  {
    tableId,
    schemaId: 1,
    fieldName: "城市",
    field: "city",
    fieldType: "STRING",
    level1: "ceshi1",
    level2: "",
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: "城市111",
    field: "city",
    fieldType: "STRING",
    level1: "",
    level2: "",
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: "城市城市城市城",
    field: "city",
    fieldType: "STRING",
    level1: "level-has-level2",
    level2: "",
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: "城市很长很长很长很长很长很长很长很长很长很长很长",
    field: "city",
    fieldType: "STRING",
    level1: "level-has-level2",
    level2: "level2-1",
    isEnum: true
  },
  {
    tableId,
    schemaId: 2,
    fieldName: "事件名称",
    field: "eventName",
    fieldType: "STRING",
    level1: "level-has-level2",
    level2: "level2-1",
    isEnum: true
  },
  {
    tableId,
    schemaId: 3,
    fieldName: "生日时间",
    field: "birthDay",
    fieldType: "DATETIME",
    level1: "level-has-level2",
    level2: "level2-1"
  },
  {
    tableId,
    schemaId: 3,
    fieldName: "生日",
    field: "birthDay",
    fieldType: "DATE",
    level1: "level-has-level2",
    level2: "level2-1"
  },
  {
    tableId,
    schemaId: 4,
    fieldName: "年龄",
    field: "age",
    fieldType: "INT",
    level1: "level-has-level2",
    level2: ""
  },
  {
    tableId,
    schemaId: 5,
    fieldName: "国家",
    field: "country",
    fieldType: "STRING",
    level1: "level-long",
    level2: ""
  }
];

const filterValue = {
  connector: "AND",
  filters: [
    {
      connector: "OR",
      filters: [
        {
          tableId,
          schemaId: 4,
          fieldName: "年龄",
          field: "age",
          fieldType: "INT",
          level1: "level-has-level2",
          level2: "",
          operator: "EQ",
          isEnum: true,
          value: "11"
        },
        {
          tableId,
          schemaId: 5,
          fieldName: "城市很长很长很长很长很长很长很长很长很长很长很长",
          field: "country",
          fieldType: "STRING",
          level1: "level-long",
          level2: "",
          operator: "EQ",
          isEnum: true,
          value: "北京"
        }
      ]
    },
    {
      connector: "OR",
      filters: [
        {
          tableId,
          schemaId: 2,
          fieldName: "事件名称",
          field: "eventName",
          fieldType: "STRING",
          level1: "level-has-level2",
          level2: "level2-1",
          isEnum: true,
          operator: "EQ",
          value: "页面浏览"
        },
        {
          tableId,
          schemaId: 4,
          fieldName: "年龄",
          field: "age",
          fieldType: "INT",
          level1: "level-has-level2",
          level2: "",
          operator: "BETWEEN",
          value: [0, 100]
        }
      ]
    }
  ]
};

export default {
  name: "TestFilter",
  components: {
    CPNTFilter
  },
  data() {
    return {
      value: filterValue,
      mode: "edit",
      jsonData: "{}",
      dataProvider: {
        getPropertyList: (name) => {
          return propertyList;
        },
        getPropertyEnumList: (tableId, schemaId) => {
          const propertyItem = _.find(propertyItemList, (v) => v.tableId === tableId && v.schemaId === schemaId);
          return propertyItem ? propertyItem.items : [];
        }
      }
    };
  },
  methods: {
    onChange(value) {
      console.log("🚀 ~ onChange ~ value:", value);
      this.value = value;
    },
    toggleMode() {
      const newMode = this.mode === "edit" ? "detail" : "edit";
      console.log("TestFilter 切换模式:", this.mode, "->", newMode);
      this.mode = newMode;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.jsonData = JSON.stringify(newVal);
      },
      deep: true
    }
  }
};
</script>
