<template>
  <div class="p-20">
    <div class="flex gap-24 mb-24">
      <a-button @click="$router.push('/test/event')">event</a-button>
      <a-button @click="$router.push('/test/filter')">filter</a-button>
      <a-button @click="$router.push('/test/actioncollective')"> actioncollective </a-button>
      <a-button @click="$router.push('/test/segment')"> segment </a-button>
      <a-button @click="$router.push('/test/label')"> label </a-button>
    </div>

    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: "Test"
};
</script>

<style scoped></style>
