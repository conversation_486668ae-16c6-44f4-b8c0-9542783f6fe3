<template>
  <div class="test-event-page">
    <h1>Event Filter 测试页面</h1>
    <div class="test-section">
      <h3>控制面板:</h3>
      <a-button @click="toggleMode"> 切换模式 (当前: {{ mode }}) </a-button>
      <a-button @click="clearFilter" style="margin-left: 8px"> 清空过滤器 </a-button>
    </div>

    <div class="flex">
      <div class="w-[85%]">
        <div class="test-section">
          <h2>Vue2 版本的 Event Filter</h2>
          <EventFilter
            :value="filterValue"
            :dataProvider="mockDataProvider"
            :onChange="handleFilterChange"
            :mode="mode"
            :showInitLine="true"
            :isActionCollection="false"
          />
        </div>

        <div class="test-section">
          <h3>当前过滤器值:</h3>
          <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre>
        </div>
      </div>

      <div class="w-[15%]">
        <!-- 右侧JSON编辑区域 -->
        <div class="json-editor">
          <a-button @click="submitJsonData" style="margin-bottom: 10px"> 应用JSON数据 </a-button>
          <a-button @click="setDemoValue" style="margin-bottom: 10px"> 设置demo数据 </a-button>
          <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EventFilter from "@/components/event/Filter.vue";
import FilterModelUtil from "@/components/event/FilterModelUtil";
import {
  tableId,
  propertyItemList,
  propertyList,
  eventDatas,
  functions,
  eventProperty,
  demoValue
} from "./TestEventConfig";

export default {
  name: "TestEvent",
  components: {
    EventFilter
  },
  data() {
    return {
      jsonData: "{}",
      filterValue: {},
      mode: "edit",
      mockDataProvider: {
        getEventList: async (searchText) => {
          // 模拟事件列表数据
          return eventDatas.body;
        },
        getEventPropertyList: async (searchText, eventId) => {
          // 模拟事件属性列表数据
          return [
            {
              field: "user_id",
              fieldName: "用户ID",
              fieldType: "STRING",
              level1: "用户属性",
              level2: "基础信息",
              isEnum: false
            },
            {
              field: "user_id",
              fieldName: "商品名称",
              fieldType: "STRING",
              level1: "事件属性",
              level2: "基础信息",
              isEnum: false
            },
            {
              field: "age",
              fieldName: "年龄",
              fieldType: "INT",
              level1: "用户属性",
              level2: "基础信息",
              isEnum: false
            },
            {
              field: "city",
              fieldName: "城市",
              fieldType: "STRING",
              level1: "用户属性",
              level2: "地理信息",
              isEnum: true
            }
          ];
        },
        getPropertyList: async (name) => {
          // 模拟属性列表数据
          return propertyList;
        }
      }
    };
  },
  watch: {
    filterValue: {
      handler(newVal) {
        console.log("filterValue changed:", newVal);
      }
    }
  },
  methods: {
    handleFilterChange(validJson, fullValue) {
      console.log("Filter changed:", validJson, fullValue);
      this.filterValue = validJson || {};
    },

    toggleMode() {
      this.mode = this.mode === "edit" ? "detail" : "edit";
    },

    clearFilter() {
      this.filterValue = {};
    },

    submitJsonData() {
      // 设置value
      this.filterValue = JSON.parse(this.jsonData);
    },

    setDemoValue() {
      this.filterValue = demoValue;
    }
  },
  mounted() {
    // 初始化一个空的过滤器
    this.filterValue = FilterModelUtil.initCreateFilterGroup(true, false);

    window.setFilterValue = this.setDemoValue;
  }
};
</script>

<style scoped>
.test-event-page {
  padding: 20px;
  /* max-width: 1200px; */
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h2,
.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
