<template>
  <div class="test-actioncollective-page">
    <h1>ActionCollective Filter 测试页面</h1>
    <div class="test-section">
      <h3>控制面板:</h3>
      <a-button @click="toggleMode"> 切换模式 (当前: {{ mode }}) </a-button>
      <a-button @click="clearFilter" style="margin-left: 8px"> 清空过滤器 </a-button>
      <a-button @click="validateFilter" style="margin-left: 8px"> 验证过滤器 </a-button>
    </div>

    <div class="flex">
      <div class="w-[85%]">
        <div class="test-section">
          <h2>Vue2 版本的 ActionCollective Filter</h2>
          <ActionCollective
            ref="actionCollectiveRef"
            :value="filterValue"
            :dataProvider="mockDataProvider"
            :onChange="handleFilterChange"
            :mode="mode"
            :showInitLine="true"
            :isActionCollection="true"
            :isUserGroup="false"
          />
        </div>

        <div class="test-section">
          <h3>当前过滤器值:</h3>
          <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre>
        </div>
      </div>

      <div class="w-[15%]">
        <!-- 右侧JSON编辑区域 -->
        <div class="json-editor">
          <a-button @click="submitJsonData" style="margin-bottom: 10px"> 应用JSON数据 </a-button>
          <a-button @click="setDemoValue" style="margin-bottom: 10px"> 设置demo数据 </a-button>
          <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ActionCollective from "@/components/actioncollective/actioncollective.vue";
import { demoValue2, mockDataProvider } from "./TestEventConfig";
import { labelMockDataProvider } from "./testLabelConfig";

export default {
  name: "TestActioncollective",
  components: {
    ActionCollective
  },
  data() {
    return {
      jsonData: "{}",
      filterValue: {},
      mode: "edit",
      mockDataProvider: { ...mockDataProvider, ...labelMockDataProvider }
    };
  },
  watch: {
    filterValue: {
      handler(newVal) {
        console.log("filterValue changed:", newVal);
        this.jsonData = JSON.stringify(newVal, null, 2);
      },
      deep: true
    }
  },
  methods: {
    handleFilterChange(newValue) {
      console.log("Filter changed:", newValue);
      this.filterValue = newValue || {};
    },

    toggleMode() {
      this.mode = this.mode === "edit" ? "detail" : "edit";
    },

    clearFilter() {
      this.filterValue = {
        connector: "AND",
        filters: []
      };
    },

    validateFilter() {
      if (this.$refs.actionCollectiveRef) {
        const isValid = this.$refs.actionCollectiveRef.isValid();
        console.log("isValid:", isValid);
        this.$message[isValid ? "success" : "error"](`验证结果: ${isValid ? "通过" : "失败"}`);
      }
    },

    submitJsonData() {
      try {
        this.filterValue = JSON.parse(this.jsonData);
      } catch (error) {
        this.$message.error("JSON格式错误: " + error.message);
      }
    },

    setDemoValue() {
      this.filterValue = demoValue2;
    }
  },
  mounted() {
    // 初始化一个空的过滤器
    this.filterValue = demoValue2;

    window.setActionCollectiveValue = this.setDemoValue;
  }
};
</script>

<style scoped>
.test-actioncollective-page {
  padding: 20px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h2,
.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.json-editor {
  padding: 10px;
}

.flex {
  display: flex;
  gap: 20px;
}

.w-\[85\%\] {
  width: 85%;
}

.w-\[15\%\] {
  width: 15%;
}
</style>
