<template>
  <a-layout-header class="nav-bar">
    <div class="logo">
      <img src="@/assets/xingye.jpg" alt="logo" width="156" height="34" />

      <span class="separator"></span>

      <span class="system-name">AI中心</span>
    </div>

    <div class="main-center-nav">
      <div class="nav-item" @click="$router.push('/')">门户</div>
      <div class="nav-item" :class="{ active: $route.path.includes('/home') }" @click="$router.push('/home')">
        主动营销
      </div>
      <div class="nav-item">智能推荐</div>
      <div class="nav-item">智能搜索</div>
      <div class="nav-item">智能海报</div>
    </div>

    <div class="icon-group">
      <a-icon type="bell" class="nav-icon" />
      <a-icon type="user" class="nav-icon" />
      <a-icon type="export" class="nav-icon" />
    </div>
  </a-layout-header>
</template>

<script>
export default {
  name: "NavBar",
  computed: {
    currentRoute() {
      return this.$route.path;
    }
  },
  methods: {},
  mounted() {}
};
</script>

<style scoped lang="scss">
$primary-color: #5478ba;
$hover-color: #dbeafe;
$hover-bg-color: #463dab;
$text-color: white;
$transition-duration: 0.2s;

.nav-bar {
  background: $primary-color;
  display: flex;
  padding: 0 24px;
  align-items: center;
  /* justify-content: space-between; */
}

.logo {
  display: flex;
  align-items: center;
  color: $text-color;
  font-size: 18px;
  font-weight: bold;
  .system-name {
    margin-right: 24px;
  }
  .separator {
    width: 1px;
    height: 20px;
    background-color: $text-color;
    margin: 0 12px;
  }
  /* margin-right: 40px; */
}

.main-center-nav {
  display: flex;
  align-items: center;
  gap: 24px;
  color: $text-color;

  .nav-item {
    cursor: pointer;
    transition: color $transition-duration;
    padding: 0 5px;

    &:hover {
      color: $hover-color;
      background: $hover-bg-color;
    }
  }
  .active {
    color: $hover-color;
    background: $hover-bg-color;
  }
}

.icon-group {
  display: flex;
  gap: 24px;
  margin-left: auto;

  .nav-icon {
    cursor: pointer;
    transition: color $transition-duration;
    color: $text-color;
    font-size: 20px;

    &:hover {
      color: $hover-color;
    }
  }
}
</style>
