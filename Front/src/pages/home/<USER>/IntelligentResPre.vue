<!--
 * <AUTHOR> 李帅
 * @Date         : 2025年06月19
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月19
 * @Description  : 
-->

<template>
  <div class="m-[10px] box-border">
    <!-- 标题 -->
    <div class="flex items-center justify-between p-[10px]">
      <h2 class="py-2 text-xl font-bold">智能指标预测</h2>
    </div>
    <!-- 筛选条件 -->
    <div class="bg-white flex flex-wrap justify-end p-[24px] pl-[0px] mb-[20px]">
      <div class="flex flex-wrap flex-start">
        <div class="flex mb-14">
          <div class="min-w-[84px] leading-[32px] ml-[24px]">策略名称：</div>
          <a-input placeholder="请输入"/>
        </div>
        <div class="flex mb-14">
          <div class="min-w-[84px] leading-[32px] ml-[24px]">用户ID类型：</div>
          <a-select default-value="CIFID" class="w-[198px]">
            <a-select-option key="1" value="default">CIFID</a-select-option>
          </a-select>
        </div>
        <div class="flex mb-14">
          <div class="min-w-[84px] leading-[32px] ml-[24px]">更新者：</div>
          <a-input placeholder="请输入"/>
        </div>
        <div class="flex mb-14">
          <div class="min-w-[84px] leading-[32px] ml-[24px]">创建时间：</div>
          <a-date-picker class="w-[198px]"/>
        </div>
      </div>
      <div class="flex">
        <a-button type="primary" class="mr-[24px]">查询</a-button>
        <a-button>清空</a-button>
      </div>
    </div>
    <!-- 列表 -->
    <div class="bg-white p-[10px]">
      <a-table :columns="columns" :data-source="data" :scroll="{ x: 1040 }">
        <div style=" text-overflow: ellipsis;white-space: nowrap;overflow: hidden;" slot="task_name" slot-scope="text">
          <!--{{ text }}-->
          <a type="link" @click="IntelligentResPreDetail"> {{ text }}</a>
        </div>
        <div slot="set" slot-scope="text">
          <a-button type="link" @click="IntelligentResPreDetail"> 查看预测</a-button>
          <a-button type="link"> 更多</a-button>
        </div>
      </a-table>
    </div>
  </div>
</template>

<script>
  import { IntelligentResPreList } from "@/mock/IntelligentResPre"
  import moment from "moment"

  const columns = [
    {
      title: "策略ID",
      dataIndex: "clid",
      // sorter: true,
      width: 75,
      fixed: "left"
    },
    {
      title: "策略名称",
      dataIndex: "task_name",
      // className: "name",
      width: 285,
      scopedSlots: { customRender: "task_name" },
      fixed: "left"
    },
    {
      title: "群体ID",
      dataIndex: "crowd_code",
      sorter: false,
      width: 120
    },
    /*{
      title: "用户ID类型",
      dataIndex: "idType",
      sorter: false,
      width: 120
    },*/
    {
      title: "策略开始时间",
      dataIndex: "start_time",
      width: 180,
      customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: true
    },
    {
      title: "策略结束时间",
      dataIndex: "end_time",
      width: 180,
      customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: true
    },
    /* {
       title: "创建者",
       width: 150,
       dataIndex: "createUserName"
     },
     {
       title: "创建时间",
       dataIndex: "createTime",
       width: 180,
       customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
       sorter: true
     },*/
    /*{
      title: "更新者",
      width: 150,
      dataIndex: "updateUserName"
    },
    {
      title: "更新时间",
      dataIndex: "updateTime",
      width: 180,
      customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: true
    },*/
    {
      title: "操作",
      dataIndex: "set",
      className: "td-set",
      fixed: "right",
      width: 180,
      scopedSlots: { customRender: "set" }
    }
  ]
  export default {
    name: "IntelligentResultPrediction",
    data() {
      return {
        data: IntelligentResPreList,
        columns
      }
    },

    mounted() {
    },

    methods: {
      IntelligentResPreDetail() {
        this.$router.push("/home/<USER>/detail")
      }
    }
  }
</script>

<style lang="scss" scoped>
  .ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
