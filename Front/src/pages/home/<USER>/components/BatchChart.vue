<template>
  <div style="width: 100%">
    <div id="mountNode"></div>
  </div>
</template>

<script>
import { Chart } from "@antv/g2";

export default {
  name: "Batch<PERSON><PERSON>",
  mounted() {
    this.renderChart();
  },
  methods: {
    renderChart() {
      var data = [
        {
          year: "1组",
          sales: 38,
        },
        {
          year: "2组",
          sales: 52,
        },
        {
          year: "3组",
          sales: 61,
        },
        {
          year: "4组",
          sales: 145,
        },
        {
          year: "5组",
          sales: 48,
        },
        {
          year: "6组",
          sales: 38,
        },
        {
          year: "7组",
          sales: 38,
        },
        {
          year: "8组",
          sales: 38,
        },
      ];
      var chart = new Chart({
        container: "mountNode",
        forceFit: true,
      });
      chart.options({
        type: "interval",
        autoFit: true,
        data,
        encode: { x: "year", y: "sales" },
      });
      chart.render();
    },
  },
};
</script>

<style scoped>
/* 根据需要添加样式 */
</style>
