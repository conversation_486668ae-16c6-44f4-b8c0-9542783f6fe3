<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月18 14:28:42
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月21 15:54:20
 * @Description  : 
-->
<template>
  <div class="mb-100 mt-50">
    <!-- -->
    <div class="flex items-center justify-between mt-20">
      <p>已选择ID类型：CIFID 将从所有客户中，筛选出符合以下条件的客户</p>
      <div class="flex flex-shrink-0 justify-between items-center h-[50px]">
        <div class="font-bold text-20">0</div>
        <a-button class="m-[0_20px]">预览用户</a-button>
        <a-button type="primary">数据权限</a-button>
      </div>
    </div>

    <div class="bg-white addPage">
      <div class="flex">
        <div class="flex-1">
          <p>
            商机规则 <a-icon type="info-circle" theme="filled" color="" />
            <span class="ml-20">实时计算商机最少选择一个实时数据源条件</span>
          </p>
          <div class="p-12 border-solid border-[1px] border-[#ccc] rounded-[10px] min-h-[500px]">
            <DemoFilter mode="edit" :hourOrMinute="hourOrMinute" />
          </div>
        </div>
        <div class="flex-shrink-0 ml-20 w-[26vw]">
          <p>商机信息数据</p>
          <div class="border-solid border-[1px] border-[#ccc] rounded-[10px] min-h-[500px] p-20">
            <div class="w-[100%] flex justify-end">
              <a-button @click="addDomain"> 添加 </a-button>
            </div>
            <p class="py-10 font-bold">事件属性信息数据</p>
            <a-form-model ref="dynamicValidateForm" :model="dynamicValidateForm" v-bind="formItemLayoutWithOutLabel">
              <a-form-model-item
                v-for="(domain, index) in dynamicValidateForm.domains"
                :key="domain.key"
                :label="domain.name"
                :prop="'domains.' + index + '.value'"
              >
                <div class="flex items-center justify-start">
                  <a-select v-model="dynamicValidateForm.domains[index].value" placeholder="请选择">
                    <a-select-option :value="item" :key="item" v-for="item in selectList">{{ item }} </a-select-option>
                  </a-select>
                  <span class="flex-shrink-0 px-10">属性的值</span>
                  <a-icon
                    v-if="dynamicValidateForm.domains.length > 1"
                    class="flex-shrink-0"
                    type="minus-circle-o"
                    :disabled="dynamicValidateForm.domains.length === 1"
                    @click="removeDomain(domain)"
                  />
                </div>
              </a-form-model-item>
            </a-form-model>
            <h4 class="font-bold">标签信息数据</h4>
            <p class="pl-20 text-[#000000a6]">消费能力等级 标签的值</p>
            <h4 class="font-bold">决策模型信息数据</h4>
            <div class="flex items-center justify-start w-[100%]">
              决策模型名称：
              <a-select placeholder="请选择模型" class="flex-shrink-0 w-200">
                <a-select-option value="1"> 资产提升潜力 </a-select-option>
                <a-select-option value="2"> 理财购买潜力 </a-select-option>
                <a-select-option value="3"> 贷款意愿度 </a-select-option>
                <a-select-option value="4"> 营销敏感度 </a-select-option>
              </a-select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white p-[10px] mt-50">
      <a-table :columns="columns" :data-source="data" key="12" :scroll="{ x: 800 }">
        <div slot="name" slot-scope="text">
          <a-button type="link" @click="handleCreateBusiness('look')">
            {{ text }}
          </a-button>
        </div>
        <div slot="status" slot-scope="text">
          <span v-if="text == 'DRAFT'">草稿</span>
          <span v-else-if="text == 'NORMAL'" class="text-[#87d068]">正常</span>
        </div>
        <div slot="calcStatus" slot-scope="text">
          <span>{{ text }}</span>
          <span v-if="text == 'DRAFT'">草稿</span>
          <span v-else-if="text == 'NORMAL'" class="text-[#87d068]">正常</span>
        </div>

        <div slot="set" slot-scope="text">
          <a-button type="link"> 查看 </a-button>
          <a-button type="link"> 编辑 </a-button>
          <a-button type="link"> 更多 </a-button>
        </div>
      </a-table>
    </div>
    <!-- foolter -->
    <div
      class="footer bg-white p-[20px_30px] fixed bottom-0 left-0 w-full z-10 flex justify-between"
      style="box-shadow: 0 -4px 10px #0000001f"
    >
      <a-button @click="handOut">退出 </a-button>
      <div>
        <a-button @click="handleStorage">暂存 </a-button>
        <a-button @click="handleBack" class="mx-20">上一步 </a-button>
        <a-button type="primary" @click="handleSubmit">保存 </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { businessList } from "@/mock/business";
import DemoFilter from "@/pages/test/DemoFilter.vue";
import moment from "moment";
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
const columns = [
  {
    title: "用户ID",
    dataIndex: "id",
    sorter: true,
    width: 180
  },
  {
    title: "ID类型",
    dataIndex: "id1",
    sorter: true,
    width: 180
  },
  {
    title: "注册日期",
    dataIndex: "updateTime",
    width: 180,
    customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
    sorter: true
  },
  {
    title: "客户名称",
    dataIndex: "name",
    className: "name",
    width: 180,
    scopedSlots: { customRender: "name" }
  },
  {
    title: "年龄",
    dataIndex: "customerCount",
    width: 100
  },
  {
    title: "民族描述",
    dataIndex: "customerCount1",
    width: 100
  },
  {
    title: "客户来源",
    dataIndex: "customerCount2",
    width: 100
  },
  {
    title: "归属网点",
    dataIndex: "customerCount3",
    width: 100
  },
  {
    title: "学历描述",
    dataIndex: "customerCount4",
    width: 100
  },
  {
    title: "最高学历",
    dataIndex: "customerCount5",
    width: 100
  },
  {
    title: "客户评级描述",
    dataIndex: "customerCount6",
    width: 250
  }
];
export default {
  name: "WorkspaceJsonFilterCondition",
  props: ["config", "hourOrMinute"],
  components: {
    DemoFilter
  },
  data() {
    return {
      selectList: ["商品名称", "商品金额", "是否注册用户", "设备类型", "商品类型", "客户编号"],
      formItemLayoutWithOutLabel: {
        labelCol: { span: 8 },
        wrapperCol: { flex: "1 " }
      },
      dynamicValidateForm: {
        domains: [
          {
            value: "商品名称",
            name: "页面浏览 事件",
            key: Date.now()
          },
          {
            value: "设备类型",
            name: "领取优惠券 事件",
            key: Date.now()
          }
        ]
      },
      columns: columns.map((v) => {
        return { ...v, key: generateUniqueId() };
      }),
      data: [],
      fileObj: { ...this.config }
    };
  },

  mounted() {},

  methods: {
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: "",
        name: "页面浏览事件",
        key: Date.now()
      });
    },
    handOut() {
      alert("退出");
    },
    handleStorage() {
      this.$message.success("暂存成功");
      setTimeout(() => {
        this.$router.push(this.fileObj.router || "/home");
      }, 500);
    },
    // 上一步
    handleSubmit() {
      this.$message.success("保存成功");
      debugger;
      setTimeout(() => {
        this.$router.push(this.fileObj.router || "/home");
      }, 500);
    },
    // 上一步
    handleBack() {
      this.upload();
    },
    upload() {
      this.$emit("updated", this.fileObj);
    }
  }
};
</script>
