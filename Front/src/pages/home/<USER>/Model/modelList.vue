<template>
  <div class="pl-[24px]">
    <a-breadcrumb class="mt-[24px]">
      <a-breadcrumb-item><span @click="toOpp">模型挖掘商机</span></a-breadcrumb-item>
      <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]"><span @click="toScene">模型业务场景</span></a-breadcrumb-item>
      <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]" v-if="id==12" >人群扩散场景</a-breadcrumb-item>
      <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]"  v-if="id==13">资产提升场景</a-breadcrumb-item>
    </a-breadcrumb>
    <h2 class="mt-[24px]" v-if="id==12">人群扩散场景</h2>
    <h2 class="mt-[24px]" v-if="id==13">资产提升场景</h2>
    <div>可选模型 (1)</div>
    <div class="modelList">
      <div class="main_body" v-if="id==12">
        <div class="img">
          <img src="../../../../assets/opp/model1.png" />
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo" >人群扩散预测模型</p>
          <div class="min-h-[85px]">
            <span class="source">专家模型</span>
            <span class="" >通过种子用户进行人群相似度预测</span>
          </div>
          <div class="btns">
            <a-button @click="toTaskList(12)">任务列表 (15)</a-button>
            <a-button class="bg-[#5478ba] text-[#fff]" @click="toCreate(12)">创建任务</a-button>
          </div>
        </div>
      </div>
      <div class="main_body" v-if="id==13">
        <div class="img">
          <img src="../../../../assets/opp/model1.png" />
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo" >资产提升预测模型</p>
          <div class="min-h-[85px]">
            <span class="source">专家模型</span>
            <span class="leading-7" >该模型按照客户资产度预测未来一段时间内可进行资产提升的概率</span>
          </div>
          <div class="btns">
            <a-button @click="toTaskList(13)">任务列表 (15)</a-button>
            <a-button class="bg-[#5478ba] text-[#fff]" @click="toCreate(13)">创建任务</a-button>
          </div>
        </div>
      </div>

    </div>
   
  </div>
</template>
<script>
  export default {
    data(){
      return {
       id:null
      }
    },
    mounted(){
      this.id = this.$route.params.id
    },
    methods: {
      toScene(){
        this.$router.push("/home/<USER>/model_opp/model/scene_select")
      },
      toTaskList(id){
        this.$router.push("/home/<USER>/model_opp/model/task/"+id)
      },
      toCreate(id){
        this.$router.push("/home/<USER>/model_opp/model/task/create/"+id)
      },
      toOpp(){
        this.$router.push("/home/<USER>/model_opp" )

      }
    },


  }
</script>
<style lang="scss" scoped>
.modelList{
  .main_body {
    width: 320px;
    float: left;
    background: #fff;
    border-radius: 6px;
    margin: 12px 12px 12px;
    color: #000000;
    line-height: 0;
    outline: 1px solid #fff;
    box-sizing: content-box;
    &:hover {
      outline: 2px solid #ff8000;
      box-sizing: content-box;
    }

    .img {
      width: 100%;
      height: 202px;
      text-align: center;
      img {
        width: 100%;
        height: 100%;
        border-radius: 6px 6px 0 0;
      }
    }

    .info {
      /* height: 400px; */
      padding: 20px 16px 16px 16px;
      .title {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: bold;
        line-height: 28px;
        height: 56px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2; 
        -webkit-box-orient: vertical;
        white-space: normal;
        cursor: pointer;
      }
    }

  
  }
  .source {
    display: inline-block;
    height: 24px;
    font-size: 12px;
    color: #1677ff;
    background: #e6f4ff;
    line-height: 24px;
    padding: 0 4px 1px 4px;
    border-radius: 3px;
    margin-right: 4px;
  }
  .btns{
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px; 
    cursor: pointer;
    button {
      width: 115px;
      border-radius: 6px;
    }
  }

}
</style>