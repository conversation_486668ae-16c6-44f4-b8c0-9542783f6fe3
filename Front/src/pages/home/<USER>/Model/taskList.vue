<template>
  <div class=" box-border">
    <!-- 标题 -->
    <div class="bg-white pl-[24px] pt-[3px] pr-[24px]">
      <a-breadcrumb class="mb-[12px] mt-[18px]">
        <a-breadcrumb-item><span @click="toOpp">模型挖掘商机</span></a-breadcrumb-item>
        <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]"><span @click="toScene">模型业务场景</span></a-breadcrumb-item>
        <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]" v-if="id==12" ><span @click="toModel(12)">人群扩散场景</span></a-breadcrumb-item>
        <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]"  v-if="id==13" ><span @click="toModel(13)">资产提升场景</span></a-breadcrumb-item>
        <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]"  v-if="id==13" >资产提升预测模型</a-breadcrumb-item>
        <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]"  v-if="id==12" >人群扩散预测模型</a-breadcrumb-item>
      </a-breadcrumb>
      <div class="pb-[8px] flex justify-between align-center">
        <h2 class="py-2 text-xl font-bold" v-if="id==13">资产提升预测模型</h2>
        <h2 class="py-2 text-xl font-bold" v-if="id==12">人群扩散预测模型</h2>
        <div>
          <a-button class=" mr-[24px]">筛选</a-button>
          <a-button class="bg-[#5478ba] text-[#fff]" @click="toCreate">创建任务</a-button>
        </div>
      
      </div>
      
    </div>
    <!-- 查询 -->
    <div class="m-[10px] bg-white p-[24px] pl-[0px] mb-[20px] mt-[20px]">
      <div>
        <a-row class="w-[100%]">
          <a-col :span="6" class="flex mt-[24px]">
            <div class="min-w-[100px] leading-[32px] ml-[24px] text-right">任务名称：</div>
            <a-input placeholder="请输入"/>
          </a-col>
          <a-col :span="6" class="flex mt-[24px]">
            <div class="min-w-[100px] leading-[32px] ml-[24px] text-right">用户ID类型：</div>
            <a-select default-value="lucy" class="w-[198px]">
              <a-select-option key="1" value="default">默认场景[default]</a-select-option>
              <a-select-option key="2" value="phone">手机号[phone]</a-select-option>
              <a-select-option key="3" value="openId">微信[openId]</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6" class="flex mt-[24px]">
            <div class="min-w-[100px] leading-[32px] ml-[24px] text-right">当前运行状态：</div>
            <a-select default-value="lucy" class="w-[198px]">
              <a-select-option key="1" value="success">运行完成</a-select-option>
              <a-select-option key="2" value="wait">待运行</a-select-option>
              <a-select-option key="3" value="running">运行中</a-select-option>
              <a-select-option key="3" value="error">运行错误</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6" class="flex mt-[24px]">
            <div class="min-w-[100px] leading-[32px] ml-[24px] text-right">更新者：</div>
            <a-input placeholder="请输入"/>
          </a-col>
          <a-col :span="6" class="flex mt-[24px] text-right">
            <div class="min-w-[100px] leading-[32px] ml-[24px]">创建时间：</div>
            <a-date-picker class="w-[298px]"/>
          </a-col>
          <a-col :span="6" class="flex mt-[24px] text-right">
            <div class="min-w-[100px] leading-[32px] ml-[24px]">最近计算时间：</div>
            <a-date-picker class="w-[198px]"/>
          </a-col>
        </a-row>
      </div>
      <div class="flex justify-end">
        <a-button type="primary" class="mr-[24px]">查询</a-button>
        <a-button>清空</a-button>
      </div>
    </div>
    <!-- 列表 -->
    <div class=" m-[10px] bg-white p-[10px]">
      <a-table :columns="columns" :data-source="id==12?taskList2:taskList" :scroll="{ x: 1700 }">
        <div style=" text-overflow: ellipsis;white-space: nowrap;overflow: hidden;" slot="task_name" slot-scope="text">
          <a type="link" > {{ text }}</a>
        </div>
        <div slot="calcStatus" slot-scope="calcStatus">
          <a-badge v-if="calcStatus === 'wait'"  color="#808080"text="待运行"  />
          <a-badge v-if="calcStatus === 'success'" status="default"  color="#87d068"text="运行完成"  />
          <a-badge v-if="calcStatus === 'running'" status="default"  color="#108ee9"text="运行中"  />
          <a-badge v-if="calcStatus === 'error'" status="default"  color="#f50"text="运行错误"  />
          <span v-if="!calcStatus">一</span>
        </div>
        <div slot="set" slot-scope="text">
          <a-button type="link" @click="toDetail"> 任务详情</a-button>
          <a-button type="link"> 更多</a-button>
        </div>
      </a-table>
    </div>
  </div>
</template>

<script>
  import { taskList, taskList2 } from "@/mock/taskList"
  import moment from "moment"

  const columns = [
    {
      title: "任务名称",
      dataIndex: "task_name",
      width: 285,
      scopedSlots: { customRender: "task_name" },
      fixed: "left"
    },
    {
      title: "用户ID类型",
      dataIndex: "idType",
      sorter: false,
      width: 120
    },
    {
      title: "计算规则",
      dataIndex: "calcRule",
      sorter: false,
      width: 120
    },
    {
      title: "当前运行状态",
      dataIndex: "calcStatus",
      sorter: false,
      width: 120,
      scopedSlots: { customRender: "calcStatus" }
    },
    {
      title: "最近计算时间",
      dataIndex: "calc_time",
      width: 180,
      customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: true
    },
    {
      title: "创建者",
      width: 150,
      dataIndex: "createUserName"
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      width: 180,
      customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: true
    },
    {
      title: "更新者",
      width: 150,
      dataIndex: "updateUserName"
    },
    {
      title: "更新时间",
      dataIndex: "updateTime",
      width: 180,
      customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: true
    },
    {
      title: "操作",
      dataIndex: "set",
      className: "td-set",
      fixed: "right",
      width: 180,
      scopedSlots: { customRender: "set" }
    }
  ]
  export default {
    name: "TaskList",
    data() {
      return {
        // taskListData:[],
        taskList,
        taskList2,
        columns,
        id:null
      }
    },

    mounted() {
      this.id = this.$route.params.id
    },

    methods: {
      taskDetail() {
      },
      toScene(){
        this.$router.push("/home/<USER>/model_opp/model/scene_select")
      },
      toModel(id){
        this.$router.push("/home/<USER>/model_opp/model/model_select/"+id)
      },
      toCreate(){
        const id = this.$route.params.id
        this.$router.push("/home/<USER>/model_opp/model/task/create/"+id)
      },
      toDetail(){
        const id = this.$route.params.id
        this.$router.push("/home/<USER>/model_opp/model/task/detail/"+id)
      },
      toOpp(){
        this.$router.push("/home/<USER>/model_opp" )

      }
    }
  }
</script>

<style lang="scss" scoped>
  .ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
