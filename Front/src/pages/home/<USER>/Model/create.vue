<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 13:01:04
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月21 17:37:43
 * @Description  : 
-->
<template>
  <div>
    <!-- header -->
    <div class="header bg-white p-[10px_20px]">
      <div class="mb-20">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/home/<USER>/model_opp"> 模型挖掘商机 </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            {{ pageType == "edit" ? "编辑" : "新增" }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <h2 class="text-[18px] font-bold">
        {{ pageType == "edit" ? "编辑" : "新增" }}商机
      </h2>
    </div>

    <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <div class="w-[30%] m-auto">
        <a-steps :current="steps" size="small">
          <a-step title="基本信息">
            <a-icon slot="icon" type="form" />
          </a-step>
          <a-step title="设置规则"> <a-icon slot="icon" type="solution" /> </a-step>
        </a-steps>
      </div>
      <!-- 第一步-->
      <div v-if="steps === 0">
        <a-form-model
          layout="vertical"
          class="mt-[20px] w-[40%] m-auto mb-[100px]"
          :model="formData"
          ref="ruleForm"
          :rules="rules"
        >
          <!-- <a-form-model-item label="业务归属" la>
            <span class="ant-form-text"> 零售 </span>
          </a-form-model-item> -->
          <a-form-model-item label="任务名称" prop="name" required>
            <a-input v-model="formData.name" placeholder="请输入任务名称"></a-input>
          </a-form-model-item>
          <a-form-model-item label="ID类型" prop="id" required>
            <a-select disabled v-model="formData.id" placeholder="请选择ID类型">
              <a-select-option value="1"> CIFID </a-select-option>
              <a-select-option value="2"> 贷款</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="有效时间" prop="timeType" required>
            <div class="flex items-center justify-between">
              <a-radio-group class="flex-shrink-0" v-model="formData.timeType">
                <a-radio value="1"> 永久有效</a-radio>
                <a-radio value="2">
                  <span class="mr-[10px]"> 有效时间</span>
                  <a-range-picker
                    class="flex-shrink-0"
                    :disabled="formData.timeType != 2"
                    v-model="formData.time"
                  />
                </a-radio>
              </a-radio-group>
            </div>
          </a-form-model-item>
          <a-form-model-item label="备注描述" prop="desc">
            <a-textarea v-model="formData.desc" placeholder="备注描述" :rows="4" />
          </a-form-model-item>
          <!-- <a-form-model-item label="标记为测试商机" prop="type">
            <a-switch v-model="formData.type" />
          </a-form-model-item> -->

          <a-form-model-item label="计算规则" prop="runturnRule">
            <div
              class="flex justify-between items-center border-[1px] rounded-[5px] border-solid overflow-hidden w-800"
              :class="formData.runturnRule == 1 ? 'border-[#ff6800]' : 'border-[#dddee1]'"
              @click="handlReturnRule(1)"
            >
              <div class="relative p-20 w-120 h-120 border-box">
                <a-icon
                  type="check"
                  class="absolute top-10 left-10 text-[#ff6800]"
                  v-show="formData.runturnRule == 1"
                />
                <img src="@/assets/opp/u2.png" class="w-[100%]" alt="" />
              </div>
              <div class="flex-1 p-10">
                <h4 class="font-bold text-18">单次更新</h4>
                <p>仅在创建完成后计算一次</p>
              </div>
            </div>
            <!-- 定时更新 -->
            <div
              class="flex justify-between py-10 items-center border-[1px] rounded-[5px] border-solid overflow-hidden mt-10 w-800"
              :class="formData.runturnRule == 2 ? 'border-[#ff6800]' : 'border-[#dddee1]'"
              @click="handlReturnRule(2)"
            >
              <div class="relative flex-shrink-0 p-20 w-120 h-120 border-box">
                <a-icon
                  type="check"
                  class="absolute top-10 left-10 text-[#ff6800]"
                  v-show="formData.runturnRule == 2"
                />
                <img src="@/assets/opp/u1.png" class="w-[100%]" alt="" />
              </div>
              <div class="flex-shrink-0 p-10">
                <h4 class="font-bold text-18">定时更新</h4>
                <p>根据设定的规则，定期自动更新</p>
              </div>
              <div class="flex-shrink-0 w-[40%] mr-100">
                <p>下次计算时间：2021-03-01 00:00:0</p>
                <div class="flex items-center justify-center">
                  <div class="flex-shrink-0 mr-10">更新频率 :</div>
                  <a-select
                    placeholder="请选择更新频率"
                    :disabled="formData.runturnRule != 2"
                  >
                    <a-select-option value="1"> 每日 </a-select-option>
                    <a-select-option value="2"> 每周</a-select-option>
                    <a-select-option value="3"> 每月</a-select-option>
                  </a-select>
                </div>
                <div class="flex items-center justify-center mt-10">
                  <div class="flex-shrink-0 mr-10">更新时间 :</div>
                  <a-select placeholder="日期" :disabled="formData.runturnRule != 2">
                    <a-select-option value="1">周一</a-select-option>
                    <a-select-option value="2">周二</a-select-option>
                    <a-select-option value="3">周三</a-select-option>
                    <a-select-option value="4">周四</a-select-option>
                    <a-select-option value="5">周五</a-select-option>
                    <a-select-option value="6">周六</a-select-option>
                    <a-select-option value="7">周日</a-select-option>
                  </a-select>
                  <a-time-picker
                    class="ml-8 w-400"
                    :disabled="formData.runturnRule != 2"
                  />
                </div>
              </div>
            </div>
          </a-form-model-item>
        </a-form-model>
        <div
          class="footer bg-white p-[20px_30px] fixed bottom-0 left-0 w-full z-10 flex justify-between"
          style="box-shadow: 0 -4px 10px #0000001f"
        >
          <a-button @click="handOut">退出 </a-button>
          <a-button type="primary" @click="handleSubmit">下一步 </a-button>
        </div>
      </div>
      <!-- 第二步 -->
      <!-- 筛选 -->
      <div v-if="steps === 1">
        <div class="text-center mt-[20px]">
          <img src="@/assets/opp/rqCreate.png" class="w-[911px]" alt="" />
          <!-- <img
            v-if="modelType === '13'"
            src="@/assets/opp/zcCreate.png"
            class="w-[911px]"
            alt=""
          /> -->
        </div>
        <div
          class="footer bg-white p-[20px_30px] fixed bottom-0 left-0 w-full z-10 flex justify-between"
          style="box-shadow: 0 -4px 10px #0000001f"
        >
          <a-button @click="handOut">退出</a-button>
          <div>
            <a-button @click="handleStorage">暂存</a-button>
            <a-button @click="handleBack" class="mx-20">上一步</a-button>
            <a-button type="primary" @click="handleSva">保存</a-button>
          </div>
        </div>
      </div>
      <!-- <FilterCondition
        v-if="steps === 1"
        :config="filterConfig"
        @updated="handleUpdated"
      /> -->
    </div>
  </div>
</template>

<script>
import FilterCondition from "@/pages/home/<USER>/components/FilterCondition.vue";
export default {
  name: "WorkspaceJsonRealTimeAdd",
  components: {
    FilterCondition,
  },
  data() {
    return {
      pageType: "add",
      steps: 0,
      formData: {
        id: "1",
        name: "",
        timeType: "",
        runturnRule: 1,
      },
      rules: {
        // id: [{ required: true, message: "请选择ID类型", trigger: "blur" }],
        name: [{ required: true, message: "请输入商机名称", trigger: "change" }],
        timeType: [{ required: true, message: "请选择有效时间", trigger: "change" }],
      },
      routes: [
        {
          // path: '/aimarketer/business/RealTime',
          breadcrumbName: "实时商机",
        },
        {
          // path: '/aimarketer/business/RealTimeAdd',
          breadcrumbName: "新增",
        },
      ],
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
      filterConfig: {
        a: 1,
        router: "/home/<USER>/model_opp",
      },
    };
  },

  mounted() {
    const type = this.$route.query.type;
    if (type == "edit") {
      this.steps = 1;
      this.pageType = type;
      this.formData = {
        id: "1",
        name: "贷款高意向人群扩散商机",
        timeType: "1",
        desc: "贷款商机",
        type: true,
        runturnRule: 1,
      };
    }
  },

  methods: {
    handlReturnRule(value) {
      this.formData.runturnRule = value;
    },
    handleUpdated(value) {
      console.log(value);
      this.steps = 0;
    },
    handOut() {
      this.$router.go(-1);
    },
    handleBack() {
      this.steps = 0;
    },
    handleStorage() {
      this.$message.success("暂存成功");
      setTimeout(() => {
        this.$router.go(-1);
      }, 500);
    },
    handleSva() {
      this.$message.success("保存成功");
      setTimeout(() => {
        this.$router.go(-1);
      }, 500);
    },
    handleSubmit(e) {
      this.steps = 1;
      return;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // alert("submit!");
          this.steps = 1;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
