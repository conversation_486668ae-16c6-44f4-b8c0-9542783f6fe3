<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 13:01:04
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月21 18:07:13
 * @Description  : 
-->
<template>
  <div>
    <!-- header -->
    <div class="header bg-white p-[10px_20px_0]">
      <div c>
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/home/<USER>/model_opp"> 模型挖掘商机 </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item
            ><router-link to="/home/<USER>/model_opp/model/scene_select">
              资产提升场景
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item
            ><router-link to="/home/<USER>/model_opp/model/model_select">
              模型业务场景
            </router-link></a-breadcrumb-item
          >
          <a-breadcrumb-item
            ><span @click="() => $router.go(-1)"
              >资产提升预测模型</span
            ></a-breadcrumb-item
          >
          <a-breadcrumb-item>
            <router-link to="/home/<USER>/model_opp/model/task/detail">
              任务详情
            </router-link></a-breadcrumb-item
          >
          <a-breadcrumb-item> 模型应用</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div class="flex items-center justify-between mt-20">
        <h2 class="text-[18px] font-bold">2024-10月资产提升目标用户预测</h2>
        <!-- <div>
          <a-button @click="onEdit"> 编辑 </a-button>
          <a-button class="ml-10"> 更多 </a-button>
        </div> -->
      </div>

      <div class="py-[20px]" style="border-bottom: 1px solid #f4ecec">
        <a-row :gutter="[24, 16]">
          <a-col :span="8">
            预测时间周期：自2020年12月1日起，未来4周（2021年12月28日
          </a-col>
          <a-col :span="8"> 模型名称：资产提升预测模型 </a-col>
          <a-col :span="8"> 模型场景：资产提升策略场景 </a-col>
          <a-col :span="8"> 用户ID类型：客户编号 </a-col>
          <a-col :span="8"> 计算规则：手动执行 立即执行 </a-col>
          <a-col :span="8"> 最近计算时间：2020-11-09 10:26:37 </a-col>
          <a-col :span="8"> 运行状态：健康</a-col>
          <a-col :span="8"> 商机状态：已上线</a-col>
          <a-col :span="8"> 归属业务：零售</a-col>
          <a-col :span="8"> 创建人：kyle.tan</a-col>
          <a-col :span="8"> 创建时间：2024-07-11 10:28:51</a-col>
          <a-col :span="8"> 更新人：kyle.tan</a-col>
          <a-col :span="8"> 2024-10月资产提升目标人群</a-col>
        </a-row>
      </div>
    </div>

    <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <div class="flex">
        <div class="flex-1">
          <p>规则 <span class="ml-20"></span></p>
          <div
            class="border-solid border-[1px] border-[#ccc] rounded-[10px] min-h-[500px] p-20"
          >
            <!-- <Area /> -->
            <img src="@/assets/opp/u91.png" class="w-[100%] h-[100%]" alt="" />
          </div>
        </div>
        <div class="flex-shrink-0 ml-20 w-400">
          <p>所有预测用户中，排名前100数据展示</p>
          <div
            class="border-solid border-[1px] border-[#ccc] rounded-[10px] min-h-[500px] p-20"
          >
            <a-table
              :columns="columns"
              :pagination="false"
              :data-source="data"
              :scroll="{ y: 650 }"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <h4 class="font-bold">模型效果</h4>

      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="历史数据验证"></a-tab-pane>
        <a-tab-pane key="2" tab="最新数据验证"> </a-tab-pane>
      </a-tabs>

      <div>
        <p>
          历史数据：2017年08月26日～2017年09月30日，按照“模型预测转化概率”由高到低排序划分为10组，展示每组实际购买人数
        </p>
        <div class="flex items-center">
          <a-card class="flex-shrink-0 mr-20">
            <div class="flex items-center">
              <div>
                <div>模型AUC值 <a-icon type="exclamation-circle" /></div>
                <div class="flex"><img class="mx-auto w-30" src="@/assets/opp/1.png" alt="" /></div>
              </div>
              <div class="text-50 ml-20 font-bold text-[#5a13fe]">0.66</div>
            </div>
          </a-card>
          <a-card>
            <div class="flex items-center">
              <div>
                <div>模型AUC值 <a-icon type="exclamation-circle" /></div>
                <div class="flex"><img class="mx-auto w-30" src="@/assets/opp/1.png" alt="" /></div>
              </div>
              <div class="text-50 ml-20 font-bold text-[#5a13fe]">0.66</div>
            </div>
          </a-card>
        </div>
        <div>
          <BatchChart  class="mx-auto w-500" />
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import Area from "../../../components/Area";
import BatchChart from "../../../components/BatchChart";

import FilterCondition from "@/pages/home/<USER>/components/FilterCondition.vue";
const list = [
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  11,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
  1,
].map((v, i) => {
  return {
    id: `0102000645281`,
    num: 0.0738,
  };
});
console.log(list, "---------------");
export default {
  components: {
    FilterCondition,
    Area,
    BatchChart,
  },
  data() {
    return {
      columns: [
        {
          title: "运营ID",
          dataIndex: "id",
          width: 180,
        },
        {
          title: "预测分数",
          dataIndex: "num",
          key: "id",
          width: 80,
        },
      ],
      data: list,
      selectList: [
        "商品名称",
        "商品金额",
        "是否注册用户",
        "设备类型",
        "商品类型",
        "客户编号",
      ],
      formItemLayoutWithOutLabel: {
        labelCol: { span: 8 },
        wrapperCol: { flex: "1 " },
      },
      dynamicValidateForm: {
        domains: [
          {
            value: "商品名称",
            name: "页面浏览 事件",
            key: Date.now(),
          },
          {
            value: "设备类型",
            name: "领取优惠券 事件",
            key: Date.now(),
          },
        ],
      },
      steps: 0,
      formData: {
        id: "1",
        name: "",
        timeType: "",
      },
      rules: {
        // id: [{ required: true, message: "请选择ID类型", trigger: "blur" }],
        name: [{ required: true, message: "请输入商机名称", trigger: "change" }],
        timeType: [{ required: true, message: "请选择有效时间", trigger: "change" }],
      },
      routes: [
        {
          // path: '/aimarketer/business/RealTime',
          breadcrumbName: "实时商机",
        },
        {
          // path: '/aimarketer/business/RealTimeAdd',
          breadcrumbName: "新增",
        },
      ],
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },

  mounted() {},
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeDomain(item) {
      let index = this.dynamicValidateForm.domains.indexOf(item);
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1);
      }
    },
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: "",
        name: "页面浏览事件",
        key: Date.now(),
      });
    },
    handleUpdated(value) {
      console.log(value);
      this.steps = 0;
    },
    handOut() {
      this.$router.go(-1);
    },
    onEdit() {
      this.$router.push({
        path: "/aimarketer/business/RealTimeAdd",
        query: {
          type: "edit",
        },
      });
    },
    handleSubmit(e) {
      this.steps = 1;
      return;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // alert("submit!");
          this.steps = 1;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
