<!--
 * <AUTHOR> 李帅
 * @Date         : 2025年06月21 15:58:04
 * @LastEditors  : 李帅
 * @LastEditTime : 2025年06月21 15:58:04
 * @Description  : 
-->
<template>
  <div>
    <!-- header -->
    <div class="header bg-white p-[10px_20px]">
      <div>
        <span class="text-[#ccc] text-[12px]" @click="tomodelOpp"> 模型挖掘商机 </span><span
        class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#ccc] text-[12px]" @click="toScene"> 模型业务场景 </span><span
        class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='12'"
              @click="toModel(12)"> 人群扩散场景 </span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='13'"
              @click="toModel(13)"> 资产提升场景 </span>
        <span class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='12'" @click="toModelList(12)"> 人群扩散预测模型 </span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='13'" @click="toModelList(13)"> 资产提升预测模型 </span>
        <span
          class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#333] text-[14px]">
          {{ pageType == "edit" ? "编辑" : "创建" }}任务
        </span>
      </div>
      <h2 class="text-[18px] font-bold mt-[8px]">
        {{ pageType == "edit" ? "编辑" : "创建" }}任务
      </h2>
    </div>

    <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <div class="w-[30%] m-auto">
        <a-steps :current="steps" size="small">
          <a-step title="基本信息">
            <a-icon slot="icon" type="form"/>
          </a-step>
          <a-step title="设置规则">
            <a-icon slot="icon" type="solution"/>
          </a-step>
        </a-steps>
      </div>
      <!-- 第一步-->
      <div v-if="steps === 0">
        <a-form-model
          layout="vertical"
          class="mt-[20px] w-[40%] m-auto mb-[100px]"
          :model="formData"
          ref="ruleForm"
          :rules="rules"
        >
          <a-form-model-item label="任务名称" prop="name" required>
            <a-input v-model="formData.name" placeholder="请输入任务名称"></a-input>
          </a-form-model-item>
          <a-form-model-item label="用户ID类型" prop="id" required>
            <a-select v-model="formData.id" placeholder="请选择ID类型">
              <a-select-option value="1"> 客户编号</a-select-option>
              <a-select-option value="2"> 手机号</a-select-option>
              <a-select-option value="3">OpenId</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="备注描述" prop="desc">
            <a-textarea v-model="formData.desc" placeholder="备注描述" :rows="4"/>
          </a-form-model-item>
          <a-form-model-item label="执行规则" prop="runturnRule">
            <div
              class="flex justify-between items-center border-[1px] rounded-[5px] border-solid overflow-hidden w-800"
              :class="formData.runturnRule == 1 ? 'border-[#ff6800]' : 'border-[#dddee1]'"
              @click="handlReturnRule(1)"
            >
              <div class="w-120 h-120 relative p-20 border-box">
                <a-icon
                  type="check"
                  class="absolute top-10 left-10 text-[#ff6800]"
                  v-show="formData.runturnRule == 1"
                />
                <img src="@/assets/opp/u2.png" class="w-[100%]" alt=""/>
              </div>
              <div class="flex-1 p-10">
                <h4 class="font-bold text-18">单次执行</h4>
                <div>
                  <a-radio-group :defaultValue="2">
                    <a-radio :value="1">立即执行</a-radio>
                    <a-radio :value="2">模型在设置的时间执行:</a-radio>
                  </a-radio-group>
                  <a-date-picker
                    v-model="startValue"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="Start"
                  />
                </div>
              </div>
            </div>
            <!-- 定时更新 -->
            <div
              class="flex justify-between py-10 items-center border-[1px] rounded-[5px] border-solid overflow-hidden mt-10 w-800"
              :class="formData.runturnRule == 2 ? 'border-[#ff6800]' : 'border-[#dddee1]'"
              @click="handlReturnRule(2)"
            >
              <div class="w-120 h-120 relative p-20 border-box flex-shrink-0">
                <a-icon
                  type="check"
                  class="absolute top-10 left-10 text-[#ff6800]"
                  v-show="formData.runturnRule == 2"
                />
                <img src="@/assets/opp/u1.png" class="w-[100%]" alt=""/>
              </div>
              <div class="flex-shrink-0 p-10">
                <h4 class="font-bold text-18">自动周期执行</h4>
                <p>设置重复执行活动的规则</p>
              </div>
              <div class="flex-shrink-0 w-[40%] mr-100">
                <p>下次计算时间：2021-03-01 00:00:0</p>
                <div class="flex justify-center items-center">
                  <div class="flex-shrink-0 mr-10">更新频率 :</div>
                  <a-select
                    placeholder="请选择更新频率"
                    :disabled="formData.runturnRule != 2"
                  >
                    <a-select-option value="1"> 每日</a-select-option>
                    <a-select-option value="2"> 每周</a-select-option>
                    <a-select-option value="3"> 每月</a-select-option>
                  </a-select>
                </div>
                <div class="flex justify-center items-center mt-10">
                  <div class="flex-shrink-0 mr-10">更新时间 :</div>
                  <a-select placeholder="日期" :disabled="formData.runturnRule != 2">
                    <a-select-option value="1">周一</a-select-option>
                    <a-select-option value="2">周二</a-select-option>
                    <a-select-option value="3">周三</a-select-option>
                    <a-select-option value="4">周四</a-select-option>
                    <a-select-option value="5">周五</a-select-option>
                    <a-select-option value="6">周六</a-select-option>
                    <a-select-option value="7">周日</a-select-option>
                  </a-select>
                  <a-time-picker
                    class="w-400 ml-8"
                    :disabled="formData.runturnRule != 2"
                  />
                </div>
              </div>
            </div>
          </a-form-model-item>
        </a-form-model>
        <div
          class="footer bg-white p-[20px_30px] fixed bottom-0 left-0 w-full z-10 flex justify-between"
          style="box-shadow: 0 -4px 10px #0000001f"
        >
          <a-button @click="handOut">退出</a-button>
          <a-button type="primary" @click="handleSubmit">下一步</a-button>
        </div>
      </div>
      <!-- 第二步 -->
      <div v-if="steps === 1">
        <div class="text-center mt-[20px]">
          <img v-if="modelType==='12'" src="@/assets/opp/rqCreate.png" class="w-[911px]" alt=""/>
          <img v-if="modelType==='13'" src="@/assets/opp/zcCreate.png" class="w-[911px]" alt=""/>
        </div>
        <div
          class="footer bg-white p-[20px_30px] fixed bottom-0 left-0 w-full z-10 flex justify-between"
          style="box-shadow: 0 -4px 10px #0000001f"
        >
          <a-button @click="handOut">退出</a-button>
          <div>
            <a-button @click="handleStorage">暂存</a-button>
            <a-button @click="handleBack" class="mx-20">上一步</a-button>
            <a-button type="primary" @click="handleSubmit">保存</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import FilterCondition from "@/pages/home/<USER>/components/FilterCondition.vue"

  export default {
    name: "ModelTaskCreate",
    components: {
      FilterCondition
    },
    data() {
      return {
        pageType: "add",
        modelType: "",
        steps: 0,
        formData: {
          id: "1",
          name: "",
          timeType: "",
          runturnRule: 1
        },
        rules: {
          // id: [{ required: true, message: "请选择ID类型", trigger: "blur" }],
          name: [{ required: true, message: "请输入商机名称", trigger: "change" }],
          timeType: [{ required: true, message: "请选择有效时间", trigger: "change" }]
        },
        routes: [
          {
            // path: '/aimarketer/business/RealTime',
            breadcrumbName: "实时商机"
          },
          {
            // path: '/aimarketer/business/RealTimeAdd',
            breadcrumbName: "新增"
          }
        ],
        formItemLayout: {
          labelCol: { span: 6 },
          wrapperCol: { span: 14 }
        },
        filterConfig: {
          a: 1,
          router: "/home/<USER>/model_opp"
        },
        startValue: "2021-08-06 00:00:00"
      }
    },

    mounted() {
      const type = this.$route.query.type
      const modelType = this.$route?.params?.id
      this.modelType = modelType
      if (type == "edit") {
        this.pageType = type
        this.formData = {
          /*name: "6月份贷款商机",
          timeType: "1",
          desc: "贷款商机",
          type: true,
          runturnRule: 1*/
        }
      }
    },

    methods: {
      tomodelOpp() {
        this.$router.push("/home/<USER>/model_opp")
      },
      toScene() {
        this.$router.push("/home/<USER>/model_opp/model/scene_select")
      },
      toModel(id) {
        this.$router.push("/home/<USER>/model_opp/model/model_select/" + id)
      },
      toModelList(id) {
        this.$router.push("/home/<USER>/model_opp/model/task/" + id)
      },
      handlReturnRule(value) {
        this.formData.runturnRule = value
      },
      handleUpdated(value) {
        console.log(value)
        this.steps = 0
      },
      handOut() {
        this.$router.go(-1)
      },
      handleStorage() {
        this.$message.success("暂存成功")
        setTimeout(() => {
          this.$router.push(this.fileObj.router || "/home")
        }, 500)
      },
      // 上一步
      handleBack() {
        this.upload()
      },
      upload() {
        this.steps = 0
      },
      handleSubmit(e) {
        this.steps === 1 && this.$router.push("/home/<USER>/model_opp/model/task/" + this.modelType)
        this.steps = 1
        return
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            // alert("submit!");
            this.steps = 1
          } else {
            console.log("error submit!!")
            return false
          }
        })
      }
    }
  }
</script>
