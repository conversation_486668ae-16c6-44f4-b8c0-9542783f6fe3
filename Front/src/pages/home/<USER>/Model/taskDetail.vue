<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 13:01:04
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月19 13:58:13
 * @Description  : 
-->
<template>
  <div>
    <!-- header -->
    <div class="header bg-white p-[10px_20px_0]">
      <div>
        <span class="text-[#ccc] text-[12px]" @click="tomodelOpp"> 模型挖掘商机 </span><span
        class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#ccc] text-[12px]" @click="toScene"> 模型业务场景 </span><span
        class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='12'"
              @click="toModel(12)"> 人群扩散场景 </span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='13'"
              @click="toModel(13)"> 资产提升场景 </span>
        <span class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='12'" @click="toModelList(12)"> 人群扩散预测模型 </span>
        <span class="text-[#ccc] text-[12px]" v-if="modelType==='13'" @click="toModelList(13)"> 资产提升预测模型 </span>
        <span
          class="text-[#ccc] p-[0_5px]">/</span>
        <span class="text-[#333] text-[14px]"> 任务详情 </span>
      </div>
      <div class="flex items-center justify-between">
        <h2 class="text-[18px] font-bold mt-[14px]">{{modelType==="12"?"2024-10月人群扩散目标用户预测":"2024-10月资产提升目标用户预测"}}</h2>
        <div>
          <a-button type="primary"> 终止模型计算</a-button>
          <a-button class="ml-10"> 更多</a-button>
        </div>
      </div>

      <div class="py-[20px]" style="border-bottom: 1px solid #f4ecec">
        <a-row :gutter="[24, 16]">
          <a-col :span="6"> 用户ID类型：客户编号</a-col>
          <a-col :span="6"> 模型名称：{{modelType==="12"?"人群扩散":"资产提升"}}预测模型</a-col>
          <a-col :span="12"> 模型场景：{{modelType==="12"?"人群扩散":"资产提升"}}策略场景</a-col>
          <a-col :span="6"> 最近计算时间：2020-11-09 10:26:37</a-col>
          <a-col :span="18"> 任务阶段：任务执行中</a-col>
          <a-col :span="6"> 创建人：kyle.tan</a-col>
          <a-col :span="18"> 创建时间：2020-11-09 10:25:17</a-col>
          <a-col :span="24">任务备注：2024-{{modelType==="12"?"人群扩散":"资产提升"}}目标人群</a-col>
        </a-row>
      </div>

      <!-- tabs -->
      <div class="flex justify-between items-center w-[100%]">
        <a-tabs class="flex-1 h-44" default-active-key="1" v-model="taskType" @change="handleUpdated">
          <a-tab-pane key="1" tab="预测结果"></a-tab-pane>
          <a-tab-pane key="2" tab="预测目标"></a-tab-pane>
        </a-tabs>
        <div>
          <a-button type="link"> 执行规则</a-button>
        </div>
      </div>
    </div>

    <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <div class="">
        <p>{{taskType === "1"?"预测结果":"预测目标"}}</p>
        <div
          class=" rounded-[10px] min-h-[200px]"
        >
          <div v-if="taskType === '1'">
            <a-table :columns="columns" :data-source="data" :scroll="{ x: 910 }">
              <div slot="calStatus" slot-scope="text">
                <!--<a-badge v-if="steps === 'normal'" :color='#108ee9'/>-->
                <a-badge v-if="text === 'normal'" color="#2db7f5"/>
                <a-badge v-if="text === 'finish'" color="#87d068"/>
                <span>{{ text==="normal"?"运行中":"运行完成" }}</span>
              </div>
              <div slot="set" slot-scope="text">
                <a-button v-if="text === 'normal'" type="link"> 停止批次</a-button>
                <a-button v-if="text === 'finish'" type="link" @click="viewModelBatchTaskDetail">查看</a-button>
              </div>
            </a-table>
          </div>
          <div v-if="taskType === '2'">
            <div class="text-center mt-[20px]">
              <img v-if="modelType==='12'" src="@/assets/opp/rqDetail.png" class="w-[909px]" alt=""/>
              <img v-if="modelType==='13'" src="@/assets/opp/zcDetail.png" class="w-[912px]" alt=""/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { modelTaskList } from "@/mock/modelTask"

  const columns = [
    {
      title: "批次ID",
      dataIndex: "clid",
      width: 100
    },
    {
      title: "运行开始时间",
      dataIndex: "startTime",
      width: 180,
      sorter: true
    },
    {
      title: "运行结束时间",
      dataIndex: "endTime",
      width: 180,
      sorter: true
    },
    {
      title: "计算状态",
      dataIndex: "calStatus",
      width: 150,
      scopedSlots: { customRender: "calStatus" }
    },
    {
      title: "预测周期",
      dataIndex: "PreCycle",
      width: 180,
      sorter: true
    },
    {
      title: "操作",
      dataIndex: "calStatus",
      className: "td-set",
      fixed: "right",
      width: 120,
      scopedSlots: { customRender: "set" }
    }
  ]
  export default {
    name: "ModelTaskDetail",
    components: {},
    data() {
      return {
        modelType: "",
        taskType: "1",
        data: modelTaskList,
        columns
      }
    },

    mounted() {
      const modelType = this.$route?.params?.id
      this.modelType = modelType
    },

    methods: {
      tomodelOpp() {
        this.$router.push("/home/<USER>/model_opp")
      },
      toScene() {
        this.$router.push("/home/<USER>/model_opp/model/scene_select")
      },
      toModel(id) {
        this.$router.push("/home/<USER>/model_opp/model/model_select/" + id)
      },
      toModelList(id) {
        this.$router.push("/home/<USER>/model_opp/model/task/" + id)
      },
      handleUpdated(value) {
        console.log(value)
        this.taskType = value
      },
      viewModelBatchTaskDetail() {
        this.$router.push("/home/<USER>/model_opp/model/task/batch/detail")
      }
    }
  }
</script>
