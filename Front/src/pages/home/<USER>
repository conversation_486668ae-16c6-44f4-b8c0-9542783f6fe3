<template>
  <div class="min-w-[700px]">
    <div class="search-box pl-[16px]">
      <a-icon type="search" class="text-[16px]" />
      <input
        type="text"
        v-model="searchText"
        placeholder="请输入搜索关键词"
        class="search-input"
      />
      <a-button class="bg-[#5478ba] text-[#fff] mr-[12px]" @click="handleSearch">搜索 </a-button>
    </div>
    <div class="mt-[24px] bt-[24px] rounded-[8px]">
      <img src="../../assets/homeBanner.png" alt="" class="w-[100%]">
    </div>
    <a-row class="bg-[#fff] p-[24px] mt-[24px] ">
      <a-col :span="6">
        <div class="mr-[12px]  items-center  flex">
          <div class="min-w-[65px]">业务线：</div>
          <a-select default-value="零售" class="w-[198px]">
            <a-select-option key="1" value="default">零售</a-select-option>
            <a-select-option key="2" value="phone">企金</a-select-option>
          </a-select>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="mr-[12px] flex items-center">
          <div class="min-w-[80px]">数据日期：</div>
          <a-range-picker  />
        </div>
      </a-col>
      <a-col :span="8">
        <div>
          <a-button>今日</a-button>
          <a-button class="mr-[12px] ml-[12px]">近7日</a-button>
          <a-button >近30日</a-button>
        </div>
      </a-col>
    </a-row>
    <!-- <div class="bg-[#fff] p-[24px] mt-[24px] flex">
      <div class="mr-[12px]">
        <span class="">业务线：</span>
        <a-select default-value="零售" class="w-[198px]">
          <a-select-option key="1" value="default">零售</a-select-option>
          <a-select-option key="2" value="phone">零售</a-select-option>
          <a-select-option key="3" value="openId">零售</a-select-option>
        </a-select>
      </div>
      <div class="mr-[12px]">
        <span>数据日期：</span>
        <a-range-picker  />
      </div>
      <div>
        <a-button>今日</a-button>
        <a-button class="mr-[12px] ml-[12px]">近7日</a-button>
        <a-button >近30日</a-button>
      </div>

    </div> -->
    <div class="model-business bg-[#fff] p-[24] mt-[24px] rounded-[8px]">
      <h5 class="text-[20px]">Ai中心商机总数</h5>
      <a-row type="flex">
        <a-col :span="14" class="business-num bg-[#F7F8FA] p-[16] h-[230px]">
          <div class="">
            <div class="text-[16px] mt-[3px]">商机总数</div>
            <div class="text-[32px] mb-[6px] font-bold">60 <span class="text-[13px]">个</span></div>
          </div>
          <div class="item flex justify-between mt-[5px]">
            <div>
              <a-badge color="#165DFF" class="dot"></a-badge>
              <span class="text-[14px]  w-[100px] inline-block">模型生成商机数</span>
            </div>
            <div class="text-[#1D2129]">50%</div>
            <div class="text-[#1D2129]">30</div>
          </div>
          <div class="item flex justify-between mt-[5px]">
            <div>
              <a-badge color="#F7BA1E" class="dot"></a-badge>
              <span class="text-[14px] w-[100px] inline-block">实时商机总数</span>
            </div>
            <div class="text-[#1D2129]">16.67%</div>
            <div class="text-[#1D2129]">10</div>
          </div>
          <div class="item flex justify-between mt-[5px]">
            <div>
              <a-badge color="#722ED1" class="dot"></a-badge>
              <span class="text-[14px]  w-[100px] inline-block">非实时商机总数</span>
            </div>
            <div class="text-[#1D2129]">33.33%</div>
            <div class="text-[#1D2129]">20</div>
          </div>
        </a-col>
        <a-col :span="9">
          <div ref="pieContainer" class="pie w-[100%] inline-block" ></div>
        </a-col>
      </a-row>
     
    </div>
    <div class="model-data bg-[#fff] p-[24] mt-[24px] rounded-[8px]">
      <h5 class="text-[20px]">模型运营数据总览</h5>
      <div class="flex">
        <div
          v-for="(item,index) in modelData" 
          :key="index" 
          :span="8" 
          :style="{background:item.bgColor}" 
          class="w-[33%] p-[16px] rounded-[8px] mr-[8px] "
        >
          <div class="mt-[-8px] text-[12px] font-bold ">{{item.name}}</div>
          <div class="text-[17px] mt-[8px] font-bold">{{ item.num }}</div>
        </div>
      </div>
    </div>
    
  </div>
</template>
<script>
import { Chart } from "@antv/g2"
export default {
  data() {
    return {
      searchText: '',
      modelData:[
        {name:'模型总个数', num: '20', bgColor:'linear-gradient(to right, rgba(114, 46, 209, 0.1),  rgba(114, 46, 209, 0.02))'},
        {name:'模型在线调用次数（次）', num: '13,266', bgColor:'linear-gradient(to right,rgba(0, 180, 42, 0.1),rgba(0, 180, 42, 0.02))'},
        {name:'覆盖人数（人）', num: '8,100,000', bgColor:'linear-gradient(to right,rgba(255, 125, 0, 0.1),rgba(255, 125, 0, 0.02))'}
      ],
      modelBusiness:[
        {item:'模型生成商机数',num:30, percent:30, color:'#165DFF'},
        {item:'实时商机总数',num:10, percent:16.67, color:'#F7BA1E'},
        {item:'非实时商机总数',num:20, percent:33.33, color:'#722ED1'},
      ]

    };
  },
  mounted(){
    const chart = new Chart({
      container: this.$refs.pieContainer,
      autoFit: true,
      height:260
    });

    chart.coordinate({ type: 'theta', outerRadius: 0.8, innerRadius: 0.5 });

    chart
      .interval()
      .data(this.modelBusiness)
      .transform({ type: 'stackY' })
      .encode('y', 'num')
      .encode('color', 'color')
      .legend(false)
      .tooltip((data) => ({
        name: data.item,
        value: data.num,
      }));

    chart.theme({
      category10:['#165DFF','#F7BA1E','#722ED1']
    })

    chart
      .text()
      .style('text', '商机总数（个）')
      .style('x', '50%')
      .style('y', '50%')
      .style('dx', 5)
      .style('dy', -15)
      .style('fontSize', 14)
      .style('fill', '#4E5969')
      .style('textAlign', 'center');

    chart
      .text()
      .style('text', '60')
      .style('x', '50%')
      .style('y', '50%')
      .style('dx', -5)
      .style('dy', 10)
      .style('fontSize', 20)
      .style('fill', '#1D2129')
      .style('textAlign', 'center');

    chart.render();
  },
  methods: {
    handleSearch() {
      console.log(this.searchText);
    },
  },
};
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  max-width: 600px;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  border-radius: 12px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
}
.business-num{
  border-radius: 12px;
  .item{
    height: 32px;
    line-height: 32px;
    border-bottom:1px dashed #E5E6EB;
  }
  .dot :deep(.ant-badge-status-dot){
    width: 8px;
    height: 8px;
  }
}

</style>