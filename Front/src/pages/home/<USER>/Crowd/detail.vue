<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 13:01:04
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月19 13:58:13
 * @Description  : 
-->
<template>
  <div>
    <!-- header -->
    <div class="header bg-white p-[10px_20px_0]">
      <div>
        <span class="text-[#ccc] text-[12px]"> 实时商机 </span>
        <span class="p-[0_5px]">/</span>
        <span class="text-[#333] text-[14px]"> 商机详情 </span>
      </div>
      <div class="flex items-center justify-between">
        <h2 class="text-[18px] font-bold">老客保险高意向商机</h2>
        <div>
          <a-button @click="onEdit"> 编辑 </a-button>
          <a-button class="ml-10"> 更多 </a-button>
        </div>
      </div>

      <div class="py-[20px]" style="border-bottom: 1px solid #f4ecec">
        <a-row :gutter="[24, 16]">
          <a-col :span="6"> D类型：客户编号 </a-col>
          <a-col :span="6"> 商机分类：保险商机 </a-col>
          <a-col :span="6"> 有效时间：永久有效 </a-col>
          <a-col :span="6"> 审批状态：已审批 </a-col>
          <a-col :span="6"> 最新商机时间：2024-07-11 10:28:51 </a-col>
          <a-col :span="6"> 运行状态：健康</a-col>
          <a-col :span="6"> 商机状态：已上线</a-col>
          <a-col :span="6"> 归属业务：零售</a-col>
          <a-col :span="6"> 创建人：kyle.tan</a-col>
          <a-col :span="6"> 创建时间：2024-07-11 10:28:51</a-col>
          <a-col :span="6"> 更新人：kyle.tan</a-col>
          <a-col :span="6"> 测试商机：否</a-col>
          <a-col :span="6"> 备注描述：只针对之前的老客户</a-col>
        </a-row>
      </div>

      <!-- tabs -->
      <div class="flex justify-between items-center w-[100%]">
        <a-tabs class="flex-1 h-44" default-active-key="1">
          <a-tab-pane key="1" tab="规则"> </a-tab-pane>
          <a-tab-pane key="2" tab="商机预览"> </a-tab-pane>
          <a-tab-pane key="3" tab="画像"> </a-tab-pane>
        </a-tabs>
        <div>
          <a-button type="link"> 数据权限 </a-button>
          <a-button type="link" class="ml-10"> 计算规则 </a-button>
        </div>
      </div>
    </div>

    <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <div class="flex">
        <div class="flex-1">
          <p>
            商机规则 <span class="ml-20">实时计算商机最少选择一个实时数据源条件</span>
          </p>
          <div
            class="border-solid border-[1px] border-[#ccc] rounded-[10px] min-h-[500px]"
          >
            <p>1231</p>
          </div>
        </div>
        <div class="flex-shrink-0 ml-20 w-[26vw]">
          <p>商机信息数据</p>
          <div
            class="border-solid border-[1px] border-[#ccc] rounded-[10px] min-h-[500px] p-20"
          >
            <!-- <div class="w-[100%] flex justify-end">
              <a-button @click="addDomain"> 添加 </a-button>
            </div> -->
            <p class="py-10">事件属性信息数据</p>
            <a-form-model
              ref="dynamicValidateForm"
              :model="dynamicValidateForm"
              v-bind="formItemLayoutWithOutLabel"
            >
              <a-form-model-item
                v-for="(domain, index) in dynamicValidateForm.domains"
                :key="domain.key"
                :label="domain.name"
                :prop="'domains.' + index + '.value'"
              >
                <div class="flex justify-start items-center">
                  <a-select
                    v-model="dynamicValidateForm.domains[index].value"
                    placeholder="请选择"
                    disabled
                  >
                    <a-select-option :key="item" :value="item" v-for="item in selectList"
                      >{{ item }}
                    </a-select-option>
                  </a-select>
                  <span class="flex-shrink-0 px-10">属性的值</span>
                  <!-- <a-icon
                      v-if="dynamicValidateForm.domains.length > 1"
                      class="flex-shrink-0"
                      type="minus-circle-o"
                      :disabled="dynamicValidateForm.domains.length === 1"
                      @click="removeDomain(domain)"
                    /> -->
                </div>
              </a-form-model-item>
            </a-form-model>
            <h4>标签信息数据</h4>
            <p class="pl-20 text-[#000000a6]">消费能力等级 标签的值</p>
            <h4>标签信息数据</h4>
            <p class="pl-20 text-[#000000a6]">消费能力等级 标签的值</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FilterCondition from "@/pages/home/<USER>/components/FilterCondition.vue";
export default {
  name: "WorkspaceJsonRealTimeAdd",
  components: {
    FilterCondition,
  },
  data() {
    return {
      selectList: [
        "商品名称",
        "商品金额",
        "是否注册用户",
        "设备类型",
        "商品类型",
        "客户编号",
      ],
      formItemLayoutWithOutLabel: {
        labelCol: { span: 8 },
        wrapperCol: { flex: "1 " },
      },
      dynamicValidateForm: {
        domains: [
          {
            value: "商品名称",
            name: "页面浏览 事件",
            key: Date.now(),
          },
          {
            value: "设备类型",
            name: "领取优惠券 事件",
            key: Date.now(),
          },
        ],
      },
      steps: 0,
      formData: {
        id: "1",
        name: "",
        timeType: "",
      },
      rules: {
        // id: [{ required: true, message: "请选择ID类型", trigger: "blur" }],
        name: [{ required: true, message: "请输入商机名称", trigger: "change" }],
        timeType: [{ required: true, message: "请选择有效时间", trigger: "change" }],
      },
      routes: [
        {
          // path: '/aimarketer/business/RealTime',
          breadcrumbName: "实时商机",
        },
        {
          // path: '/aimarketer/business/RealTimeAdd',
          breadcrumbName: "新增",
        },
      ],
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
    };
  },

  mounted() {},

  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeDomain(item) {
      let index = this.dynamicValidateForm.domains.indexOf(item);
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1);
      }
    },
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: "",
        name: "页面浏览事件",
        key: Date.now(),
      });
    },
    handleUpdated(value) {
      console.log(value);
      this.steps = 0;
    },
    handOut() {
      this.$router.go(-1);
    },
    onEdit() {
      this.$router.push({
        path: "/aimarketer/business/RealTimeAdd",
        query: {
          type: "edit",
        },
      });
    },
    handleSubmit(e) {
      this.steps = 1;
      return;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // alert("submit!");
          this.steps = 1;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
