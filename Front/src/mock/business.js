import _ from "lodash";
import moment from "moment";

function randomNum(min, max) {
  return Math.floor(Math.random() * (max - min + 1) + min);
}
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

export const getData = (data, type) => {
  const id = randomNum(20, 20);
  data = ["model", "custom"].includes(type) ? modelBusinessList : type === "offline" ? offlineBusinessList : data;
  const newData = _.shuffle(data);
  const len = newData.length;
  const list = newData.map((item, index) => {
    item["operateNum"] = ["model", "custom"].includes(type) ? randomNum(100, 1000) : 0;
    item["latesStatus"] = ["model", "custom"].includes(type) ? randomNum(0, 5) : 0;
    return {
      ...item,
      id: id - index,
      createTime: new Date().getTime(),
      updateTime: new Date().getTime(),
      personnel: randomNum(1000, 10000)
    };
  });
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        total: id,
        list
      });
    }, 1000);
  });
};

export const realTimeBusinessList = [
  {
    id: 1,
    opportunityName: "浏览理财页面断点商机",
    category: "浏览行为",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 2,
    opportunityName: "浏览贷款页面断点商机",
    category: "浏览行为",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 3,
    opportunityName: "浏览黄金页面断点商机",
    category: "浏览行为",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 4,
    opportunityName: "实时代发人金商机",
    category: "动账行为",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 5,
    opportunityName: "实时年终奖发放商机",
    category: "动账行为",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 5,
    opportunityName: "转出金额超过1万元商机",
    category: "动账行为",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  }
];

const modelBusinessList = [
  {
    id: 1,
    opportunityName: "日盈理财潜客商机",
    category: "财富商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "天天利产品潜客商机",
    category: "财富商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批通过",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "养老金开户潜客商机",
    category: "养老金商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批中",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "养老金入金潜客商机",
    category: "养老金商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批中",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "福运金潜客商机",
    category: "财富商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批中",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "寰宇金潜客商机",
    category: "财富商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批中",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "中期理财潜客商机",
    category: "财富商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批通过",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "长期理财潜客商机",
    category: "财富商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "资产临界100提升潜客商机",
    category: "资产提升商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "资产临界1W提升潜客商机",
    category: "资产提升商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "资产临界10W提升潜客商机",
    category: "资产提升商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "信用卡账单分期潜客商机",
    category: "信用卡商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "信用卡现金分期潜客商机",
    category: "信用卡商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "信用卡开卡断点商机",
    category: "信用卡商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 1,
    opportunityName: "贷款高意愿潜客商机",
    category: "贷款商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  }
];

const offlineBusinessList = [
  {
    id: 1,
    opportunityName: "过去7天看过日盈未购买商机",
    category: "基金商机",
    personnel: 15123,
    idType: "CIFID",
    runningStatus: "健康",
    status: "草稿",
    approvalStatus: "-",
    validityPeriod: "永久",
    latestTime: "2025-05-18",
    creator: "潘立仁",
    createTime: "2024-03-12",
    updater: "张金荆",
    updateTime: "2025-06-10"
  },
  {
    id: 2,
    opportunityName: "过去14天看过兴闪贷未申请的商机",
    category: "贷款商机",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 3,
    opportunityName: "过去7天转出金额超过10万元的商机",
    category: "转账商机",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "审批通过",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 4,
    opportunityName: "过去7天转入金额超过10万元的客户商机",
    category: "转账商机",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 5,
    opportunityName: "过去30天日均资产8-9万元的提升潜客",
    category: "资产商机",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 5,
    opportunityName: "基金高意愿购买潜客",
    category: "基金商机",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  },
  {
    id: 5,
    opportunityName: "保险高意向购买潜客",
    category: "保险商机",
    personnel: 10,
    idType: "CIFID",
    runningStatus: "健康",
    status: "未上线",
    approvalStatus: "待审批",
    validityPeriod: "永久",
    latestTime: "2025-04-20",
    creator: "元凌峰",
    createTime: "2024-07-01",
    updater: "潘立仁",
    updateTime: "2025-05-15"
  }
];
export const columns = [
  {
    title: "ID",
    dataIndex: "id",
    sorter: true,
    width: 80,
    fixed: "left"
  },
  {
    title: "商机名称",
    dataIndex: "opportunityName",
    width: 280,
    scopedSlots: { customRender: "name" },
    fixed: "left"
  },
  {
    title: "商机分类",
    dataIndex: "category",
    width: 180
  },
  {
    title: "商机人数",
    dataIndex: "personnel",
    width: 100
  },
  {
    title: "ID类型",
    dataIndex: "idType",
    width: 100
  },
  {
    title: "商机运行状态",
    dataIndex: "runningStatus",
    scopedSlots: { customRender: "runningStatus" },
    width: 150
  },
  {
    title: "商机状态",
    dataIndex: "status",
    scopedSlots: { customRender: "status" },
    width: 120
  },
  {
    title: "审批状态",
    dataIndex: "approvalStatus",
    customRender: (text) => text || "-",
    width: 150
  },
  {
    title: "有效时间",
    dataIndex: "validityPeriod",
    width: 180
  },
  {
    title: "最新商机时间",
    dataIndex: "latestTime",
    width: 180,
    customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
    sorter: true
  },
  {
    title: "创建人",
    width: 150,
    dataIndex: "creator"
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    width: 180,
    customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
    sorter: true
  },
  {
    title: "更新人",
    width: 150,
    dataIndex: "updater"
  },
  {
    title: "更新时间",
    dataIndex: "updateTime",
    width: 180,
    customRender: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss"),
    sorter: true
  },
  {
    title: "操作",
    dataIndex: "set",
    className: "td-set",
    fixed: "right",
    width: 230,
    scopedSlots: { customRender: "set" }
  }
];
