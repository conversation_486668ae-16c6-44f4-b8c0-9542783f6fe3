export const IntelligentResPreList = [
  {
    clid: 32266,
    task_name: "【20250401】武汉分行-分行智能外呼—月月享长尾10000-99999.99（5积点）",
    crowd_code: 1976,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 34679,
    task_name: "南昌分行-低留存代发客户推理财+活动",
    crowd_code: 1977,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 32280,
    task_name: "【20250401】武汉分行-分行智能外呼—月月享长尾10000-99999.99（5积点）",
    crowd_code: 1978,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 34681,
    task_name: "南昌分行-低留存代发客户推理财+活动",
    crowd_code: 1979,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 32282,
    task_name: "【20250401】武汉分行-分行智能外呼—月月享长尾10000-99999.99（5积点）",
    crowd_code: 1980,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 34683,
    task_name: "南昌分行-低留存代发客户推理财+活动",
    crowd_code: 1981,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 32284,
    task_name: "【20250401】武汉分行-分行智能外呼—月月享长尾10000-99999.99（5积点）",
    crowd_code: 1982,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 34685,
    task_name: "南昌分行-低留存代发客户推理财+活动",
    crowd_code: 1983,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 32286,
    task_name: "【20250401】武汉分行-分行智能外呼—月月享长尾10000-99999.99（5积点）",
    crowd_code: 1984,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  },
  {
    clid: 34687,
    task_name: "南昌分行-低留存代发客户推理财+活动",
    crowd_code: 2341,
    idType: "用户编号",
    start_time: "2025/5/1 0:00:00",
    end_time: "2025/5/1 0:00:00",
    createUserName: "kyle.tan",
    createTime: "2020-06-20 10:28:51",
    updateUserName: "kyle.tan",
    updateTime: "2020-06-20 10:28:51"
  }
]
