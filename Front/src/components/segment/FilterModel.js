import Log from '../utils/log';

const log = Log.getLogger('FilterModel');

class FilterModel {
  static counter = 0;

  constructor(type, segment) {
    this.type = type;
    this.segment = segment || {};
    log.debug('constructor', JSON.stringify(this));
  }

  static fromJson({ type, segment }) {
    log.debug('fromJson', JSON.stringify({ type, segment }));
    return new FilterModel(type, segment);
  }

  toJson() {
    const { type, segment } = this;
    return { type, segment };
  }

  setDirty(isDirty) {
    this.isDirty = isDirty;
  }

  /**
   * 校验filterModel，返回{
   *  isValid: (boolean) 是否有效
   *  type: 表示fieldType有错误，次值为错误返回值
   *  segment: 同上
   *  value: {
   *    maxLen: 错误信息
   *    required: 错误信息
   *  }
   * }
   */
  valid() {
    const { type, segment = {} } = this;

    const { id } = segment;

    const result = { isValid: true, message: [] };

    if (!type) {
      result.message.push('请选择包含关系');
      result.type = true;
      result.isValid = false;
    }

    if (!id) {
      result.message.push('请选择分群');
      result.id = true;
      result.isValid = false;
    }

    return result;
  }

  changeProperty(property) {
    this.type = property.type;
    this.segment = property.segment;
  }

  clearProperty() {
    this.type = null;
    this.segment = null;
  }
}

export default FilterModel;
