<template>
  <div>
    <a-select :value="value.type" style="width: 100%" @change="handleChange">
      <a-select-option v-for="(item, index) in optionValues" :key="item" :value="item">
        {{ optionNames[index] }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import { Select } from "ant-design-vue";
import _ from "lodash";
import FILTER_CONFIG from "./FilterConfig";

const optionValues = _.keys(FILTER_CONFIG.TYPE);
const optionNames = _.values(FILTER_CONFIG.TYPE);

export default {
  name: "FilterType",
  components: {
    "a-select": Select,
    "a-select-option": Select.Option
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      optionValues,
      optionNames
    };
  },
  methods: {
    handleChange(v) {
      this.value.changeProperty({ ...this.value, type: v });
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
/* 样式将从segment.scss中继承 */
</style>
