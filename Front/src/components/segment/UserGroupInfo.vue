<template>
  <div class="renderUserGroupInfo">
    <div class="title">{{ data.name }}</div>
    <div class="content">
      <div>计算状态：{{ getCalcStatusName(data.calcStatus) }}</div>
      <div>更新规则：{{ rule[data.calcRule] }}</div>
      <div v-if="data.calcRule === 'SCHEDULE'">
        计算规则：{{ getScheduleRule(data.scheduleConf) }}
      </div>
    </div>
    <div v-if="data.type !== 'CAMPAIGN'">
      <div class="count">{{ data.customerCount }}人</div>
      <div class="lastCalcTime">
        {{ moment(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss') }}
      </div>
    </div>
  </div>
</template>

<script>
import _ from "lodash";
import moment from "moment";

// 计算状态
const calcStatusList = [
  {
    name: "未开始",
    text: "未开始",
    key: "NOTRUN",
    value: "NOTRUN"
  },
  {
    name: "计算中",
    text: "计算中",
    value: "CALCING",
    key: "CALCING"
  },
  {
    name: "计算失败",
    text: "计算失败",
    value: "FAIL",
    key: "FAIL"
  },
  {
    name: "计算成功",
    text: "计算成功",
    value: "SUC",
    key: "SUC"
  }
];

export default {
  name: "UserGroupInfo",
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      rule: {
        ONCE: "单次更新",
        SCHEDULE: "定期更新",
        DAY: "每日",
        WEEK: "每周",
        MONTH: "每月"
      },
      moment
    };
  },
  methods: {
    getCalcStatusName(calcStatus) {
      if (!calcStatus) return "";
      const status = _.find(calcStatusList, v => v.value === calcStatus);
      return status ? status.name : "";
    },
    
    getScheduleRule(scheduleConf) {
      if (!scheduleConf || !scheduleConf.schedule) return "-";
      return this.rule[scheduleConf.schedule.scheduleRate] || "-";
    }
  }
};
</script>

<style scoped>
.renderUserGroupInfo {
  max-width: 300px;
}

.title {
  font-weight: bold;
  margin-bottom: 8px;
}

.content {
  margin-bottom: 8px;
}

.content > div {
  margin-bottom: 4px;
}

.count {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

.lastCalcTime {
  font-size: 12px;
  color: #666;
}
</style>
