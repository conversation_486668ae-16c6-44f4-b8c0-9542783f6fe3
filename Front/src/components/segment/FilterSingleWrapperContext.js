import { provide, inject } from 'vue';

const FilterSingleWrapperContextKey = Symbol('FilterSingleWrapperContext');

export const FilterSingleWrapperContextProvider = {
  name: 'FilterSingleWrapperContextProvider',
  props: {
    value: {
      type: Object,
      default: () => ({ editing: false })
    }
  },
  setup(props, { slots }) {
    provide(FilterSingleWrapperContextKey, () => props.value);
    return () => slots.default();
  }
};

export const useFilterSingleWrapperContext = () => {
  const context = inject(FilterSingleWrapperContextKey);
  if (!context) {
    return () => ({ editing: false });
  }
  return context;
};

export default FilterSingleWrapperContextKey;
