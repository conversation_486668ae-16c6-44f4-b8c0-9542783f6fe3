export const FilterContextProvider = {
  name: "FilterContextProvider",
  props: {
    value: {
      type: Object,
      required: true
    }
  },
  provide() {
    return {
      filterContext: () => this.value || {}
    };
  },
  render() {
    return this.$slots.default;
  }
};

export const useFilterContext = () => {
  // This is a placeholder for Vue 2 compatibility
  // In Vue 2, we use inject in component options
  return {};
};

export default {
  Provider: FilterContextProvider,
  useContext: useFilterContext
};
