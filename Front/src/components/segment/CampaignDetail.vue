<template>
  <div class="selectCampaignDetailContent">
    <div class="totalPeople">{{ info.customerCount }} 人</div>
    <div class="dateTime">
      计算时间
      {{ info.lastCalcTime && moment(info.lastCalcTime).format("YYYY-MM-DD HH:mm:ss") }}
    </div>
    <div class="desc">从参与过流程的用户中筛选出符合以下规则的用户</div>
    <div class="selectCampaignDetail">
      <div class="left">
        <img class="imgStyle" alt="" :src="info.connector === 'AND' ? intersection : unionset" />
        <div class="connectLine" />
      </div>
      <div class="right">
        <div class="timeCondition">
          <a-button
            :style="{ display: info?.timeCondition?.show === true ? 'none' : 'inline-block' }"
            disabled
            class="conditionButton"
            type="dashed"
          >
            设置时间条件
          </a-button>
          <div :style="{ display: info?.timeCondition?.show === false ? 'none' : 'block' }" class="conditionAll">
            <div style="margin-right: 40px">
              最近
              <span style="color: red"> {{ info?.timeCondition?.recentlyDays }} </span>
              天参加过流程
            </div>
            <a-checkbox disabled :checked="info?.timeCondition?.onlyCreatedByMe"> 仅包括我创建的流程 </a-checkbox>
          </div>
        </div>
        <div class="compaignList">
          <div name="campaignCalcLogNodes">
            <a-button
              class="plusButton"
              :style="{
                display: info.campaignCalcLogNodes && info.campaignCalcLogNodes.length > 0 ? 'none' : 'inline-block'
              }"
              disabled
              type="dashed"
            >
              <a-icon type="plus" style="margin-right: 2px" />
              添加流程
            </a-button>
            <div class="detailCompaignStyle">
              <div style="width: 70px">参与流程</div>
              <div>
                <div v-for="n in info.campaignCalcLogNodes" :key="n?.campaign?.id" style="margin-bottom: 5px">
                  <a-tag> [{{ n?.campaign?.id }}]{{ n.campaign?.name }} </a-tag>
                  <span v-if="n?.calcLogAndNodes?.length > 0">批次 </span>
                  <a-tag v-for="w in n?.calcLogAndNodes" :key="w?.calcLogId"> [{{ w?.calcLogId }}] </a-tag>
                  <span v-if="n?.calcLogAndNodes?.length === 1 && n?.calcLogAndNodes[0]?.flowNodeIds?.length > 0">
                    节点
                  </span>
                  <a-tag
                    v-for="h in n?.calcLogAndNodes?.length === 1 ? n.calcLogAndNodes[0]?.flowNodeIds : []"
                    :key="h"
                  >
                    [{{ h }}]
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="limitS">
      <a-checkbox disabled :checked="info.limits" />
      <span class="limitTitle">AI决策模型人数限制</span>
      <a-input-number disabled style="width: 160px" :min="0" :max="2000000" :value="info.limits" />
      <span style="margin-left: 8px">系统AI决策模型人数上限：2000000</span>
    </div>
  </div>
</template>

<script>
import { Button, InputNumber, Checkbox, Tag } from "ant-design-vue";
import moment from "moment";
import intersection from "../img/img2.png";
import unionset from "../img/img1.png";

export default {
  name: "CampaignDetail",
  components: {
    "a-button": Button,
    "a-input-number": InputNumber,
    "a-checkbox": Checkbox,
    "a-tag": Tag
  },
  props: {
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      intersection,
      unionset,
      moment
    };
  }
};
</script>

<style lang="scss" scoped>
@import "./segment.scss";
</style>
