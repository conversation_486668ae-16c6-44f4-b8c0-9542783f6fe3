<template>
  <div v-if="shouldRender" class="FilterGroupListPannel1">
    <FilterGroup
      :connector="value.connector"
      :onChangeConnector="onChangeConnector(value)"
      :filterCount="filterListCount"
      inner=""
    >
      <template v-for="(filterList, index) in value.filters">
        <FilterGroup
          v-if="getFilterGroupShouldRender(filterList)"
          :connector="filterList.connector"
          :onChangeConnector="onChangeConnector(filterList)"
          :filterCount="getFilterCount(filterList)"
          inner="inner"
          :key="index"
        >
          <FilterSingle
            v-for="(filter, i) in filterList.filters"
            :key="i"
            :value="filter"
            :onChange="onChangeFilter(filterList, i)"
            :onDelete="onDeleteFilter(filterList, i)"
          />
        </FilterGroup>
      </template>
    </FilterGroup>
  </div>
</template>

<script>
import FilterGroup from "./FilterGroup.vue";
import FilterSingle from "./FilterSingle.vue";
import FilterModelUtil from "./FilterModelUtil";

export default {
  name: "FilterListGroup",
  components: {
    FilterGroup,
    FilterSingle
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  computed: {
    context() {
      return this.filterContext();
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log })
        }
      );
    },
    mode() {
      return this.context.mode || "edit";
    },
    shouldRender() {
      // 没有值就退出
      if (!this.value || !this.value.filters) {
        return false;
      }

      const filterListCount = this.value.filters.length;
      return filterListCount > 0;
    },
    filterListCount() {
      return this.value.filters.length;
    }
  },
  methods: {
    onChangeFilter(filterList, index) {
      return () => {
        // filterList[index] = filter;
        this.onChange(this.value);
      };
    },

    onAddFilter(filterList, index) {
      return () => {
        filterList.filters = [
          ...filterList.filters.slice(0, index + 1),
          FilterModelUtil.createFilter(),
          ...filterList.filters.slice(index + 1)
        ];
        this.onChange(this.value);
      };
    },

    onDeleteFilter(filterList, index) {
      return () => {
        filterList.filters.splice(index, 1);
        FilterModelUtil.deleteEmptyFilterList(this.value);
        this.onChange(this.value);
      };
    },

    onChangeConnector(filter) {
      return (v) => {
        filter.connector = v;
        this.onChange(this.value);
      };
    },

    getFilterGroupShouldRender(filterList) {
      if (!filterList || !filterList.filters) return false;
      const filterCount = this.getFilterCount(filterList);
      return filterCount > 0;
    },

    getFilterCount(filterList) {
      return this.mode === "detail" ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length;
    }
  },
  mounted() {
    const log = this.logProvider.getLogger("FilterListGroup");
    log.debug("connector", this.value.connector);
    log.debug("filters", this.value.filters);
  }
};
</script>

<style scoped>
/* 样式将从segment.scss中继承 */
</style>
