<template>
  <li :class="`FilterSingle ${mode}`">
    <div style="display: flex; align-items: center" :hidden="mode !== 'edit' && !value.valid().isValid">
      <div :class="`FilterField ${mode} ${validator?.type && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="FILTER_CONFIG.TYPE[type]" :useTakePlaceWidth="true">
          <FilterType :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="segment?.name || ''" :useTakePlaceWidth="true">
          <FilterSegment :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip
          v-if="value.validating && (validator?.id || validator?.type)"
          placement="topRight"
          :title="_.head(_.values(validator.message))"
        >
          <div style="margin-right: 5px">
            <a-icon type="question-circle" class="Validator" />
          </div>
        </a-tooltip>
        <a-icon type="close-circle" class="delete" @click="onDelete" />
      </div>
      <div class="description">仅支持最新AI决策模型结果</div>
    </div>
  </li>
</template>

<script>
import { Tooltip } from "ant-design-vue";
import _ from "lodash";
import FilterSingleWrapper from "./FilterSingleWrapper.vue";
import FilterSegment from "./FilterSegment.vue";
import FilterType from "./FilterType.vue";
import FILTER_CONFIG from "./FilterConfig";

export default {
  name: "FilterSingle",
  components: {
    "a-tooltip": Tooltip,
    FilterSingleWrapper,
    FilterSegment,
    FilterType
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    onDelete: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      validator: {},
      FILTER_CONFIG,
      _
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    mode() {
      return this.context.mode || "edit";
    },
    type() {
      return this.value.type;
    },
    segment() {
      return this.value.segment || {};
    }
  },
  watch: {
    "value.type": {
      handler() {
        this.updateValidator();
      }
    },
    "segment.id": {
      handler() {
        this.updateValidator();
      }
    }
  },
  methods: {
    updateValidator() {
      if (this.mode !== "edit") return;
      // 退出编辑
      this.validator = this.value.valid();
    }
  },
  mounted() {
    this.updateValidator();
  }
};
</script>

<style scoped>
/* 样式将从segment.scss中继承 */
</style>
