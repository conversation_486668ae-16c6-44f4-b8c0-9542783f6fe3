// @import '~antd/dist/antd.css';
@import '../variable.scss';
$connectLineStyle: 1px dotted #ccc;
$connectLineMarginTopAndBottom: 15px;

.wolf-static-component_filter_FilterGroupPanel_action_collective{

  .FilterGroupPanel3 {
    // border: 1px solid #ccc;

    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    // background-color: #fafafa;
    // padding-left: 16px;
    .ConnectorPanel {
      // border: 1px solid #ccc;
      width: 40px;
      min-width: 40px;
      position: relative;
      .VLine {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        width: 0px;
        border-left: $connectLineStyle;
        z-index: 100;
      }
      .TopLine {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }
      .BottomLine {
        position: absolute;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }
      .Connector {
        position: absolute;
        left: 0;
        top: 50%;
        height: 30px;
        line-height: 30px;
        transform: translateY(-15px);
        vertical-align: middle;
        z-index: 200;
        .ant-switch {
          background-color: #ccc;
          opacity: 1;
        }
        .ant-switch-checked {
          background-color: $primary_color;
          opacity: 1;
        }
        .ant-switch-disabled {
          opacity: 1;
        }
      }
    }
    .FilterList {
      // border: 1px solid #ccc;
      flex: auto;
      margin: 0;
      padding: 0;
      margin: 2px 0;
      &.inner {
        // background-color: #fafafa;
      }
      > li {
        margin: 2px 2px;
        list-style: none;
      }
      .FilterSingle.edit {
        .takePlace {
          min-width: 50px;
          position: absolute;
          visibility: hidden;
          width: auto;
          white-space: nowrap;
        }
      }
      .FilterSingle {
        line-height: 34px;
        &:focus {
          outline: none;
        }
        > div {
          display: inline-block;
          > div {
            display: inline-block;
          }
          .FilterField,
          .FilterOperator,
          .FilterValue {
            .FilterSingleWrapper {
              word-wrap: break-word;
              margin-right: 5px;
              .valueShow {
                color: $active_color;
                word-break: break-word;
              }
            }
          }
          .Ctroller {
            font-size: 16px;
            // line-height: 28px;
            display: flex;
            align-items: center;
            .add {
              margin: 0 2px;
              color: rgba(0,0,0, .65);
            }
            .add:hover{
              color: $primary_color;
            }
            .delete {
              margin: 0 4px;
              color: rgba(0,0,0, .65);
            }
            .delete:hover{
              color: $primary_color;
            }
          }
          .Validator {
            color: $danger_color;
            font-size: 16px;
          }
        }
      }
    }
    .FilterList.inner>.FilterGroupPanel3{
      margin-bottom: 20px;
      padding: 16px;
      border: 1px solid #D9D9D9;
      border-radius: 2px;
    }

    .FilterList.inner>.FilterGroupPanel3:last-child{
      margin-bottom: 0;
    }

    &>.FilterList.inner{
      >div{
        > .wolf-static-component_filter_FilterGroupPanel_event, >.wolf-static-component_filter_FilterGroupPanel, >.wolf-static-component_filter_FilterGroupPanel_label, >.wolf-static-component_filter_FilterGroupPanel_segment{
            border: 1px solid #D9D9D9;
            border-radius: 2px;
            margin-bottom: 16px;
            padding: 8px 0 16px 8px;
        }
      }
    }

  }


  .action_collective_button_group{
    button{
      margin-right: 8px;
    }
  }

  &>.FilterGroupPanel3{
    background: none;
    padding-left: 0;
    margin-bottom: 16px;
  }

  .FilterList1.inner,.FilterList.inner{
    background: none!important;
  }
}
