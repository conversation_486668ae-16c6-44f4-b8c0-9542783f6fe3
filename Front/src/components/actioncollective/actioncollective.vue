<template>
  <div class="wolf-static-component_filter_FilterGroupPanel_action_collective">
    <ActionCollectiveGroup
      v-if="!(!showInitLine && _.isEmpty(value?.filters))"
      :connector="value.connector"
      :onChangeConnector="(res) => onChangeConnector(res, 'connector')"
      :filterCount="count()"
      inner="inner"
      :mode="mode"
    >
      <template v-for="(item, index) in value.filters">
        <ActionCollectiveGroup
          :connector="item.connector"
          :onChangeConnector="(res) => onSubConnectorChange(res, 'connector', item)"
          :filterCount="getItemKeys(item).length"
          inner="inner"
          :mode="mode"
          :key="index"
        >
          <div>
            <template v-for="key in getOrderedKeys(item)">
              <!-- 事件组 -->
              <EventFilter
                v-if="key === 'eventGroup'"
                :ref="(el) => setRef(index, key, el)"
                :dataProvider="dataProvider"
                :showInitLine="true"
                :value="item[key] || {}"
                :onChange="(v, innerValue) => onChangeItemFilter('eventGroup', v, index, innerValue)"
                :mode="mode"
                :isActionCollection="isActionCollection"
                :key="key"
              />

              <!-- 用户属性 -->
              <CPNTFilter
                v-else-if="key === 'userProperty'"
                :ref="(el) => setRef(index, key, el)"
                addButtonText="属性"
                :dataProvider="dataProvider"
                :value="item[key] || {}"
                :onChange="(v, innerValue) => onChangeItemFilter('userProperty', v, index, innerValue)"
                :mode="mode"
                :isUserGroup="isUserGroup"
                :key="key"
              />

              <!-- 用户标签 -->
              <Label
                v-else-if="key === 'userLabel'"
                :ref="(el) => setRef(index, key, el)"
                :dataProvider="dataProvider"
                :value="item[key] || {}"
                :onChange="(v, innerValue) => onChangeItemFilter('userLabel', v, index, innerValue)"
                :mode="mode"
                :isUserGroup="isUserGroup"
                :key="key"
              />

              <!-- 分群 -->
              <Segment
                v-else-if="key === 'segment'"
                :ref="(el) => setRef(index, key, el)"
                :dataProvider="dataProvider"
                :value="item[key] || {}"
                :onChange="(v, innerValue) => onChangeItemFilter('segment', v, index, innerValue)"
                :mode="mode"
                :key="key"
              />
            </template>

            <HandleGroup :value="item" :index="index" :onClick="(type) => handleAddSubItem(type, index)" :mode="mode" />
          </div>
        </ActionCollectiveGroup>
      </template>
    </ActionCollectiveGroup>

    <div class="action_collective_button_group" :style="{ display: mode === 'detail' ? 'none' : 'block' }">
      <a-button type="dashed" @click="() => onAddItemFilter('eventGroup')">
        <a-icon type="plus" />
        行为
      </a-button>
      <!-- <a-button type="dashed" @click="() => onAddItemFilter('userProperty')">
        <a-icon type="plus" />
        属性
      </a-button> -->
      <a-button type="dashed" @click="() => onAddItemFilter('userLabel')">
        <a-icon type="plus" />
        标签
      </a-button>
      <a-button type="dashed" @click="() => onAddItemFilter('segment')">
        <a-icon type="plus" />
        AI决策模型
      </a-button>
    </div>
  </div>
</template>

<script>
import { Button } from "ant-design-vue";
import _ from "lodash";
import ActionCollectiveGroup from "./ActionCollectiveGroup.vue";
import CPNTFilter from "../filter/Filter.vue";
import Label from "../label/Filter.vue";
import EventFilter from "../event/Filter.vue";
import HandleGroup from "./handlegroup.vue";
import Segment from "../segment/Filter.vue";

export default {
  name: "ActionCollective",
  components: {
    "a-button": Button,
    ActionCollectiveGroup,
    CPNTFilter,
    Label,
    EventFilter,
    HandleGroup,
    Segment
  },
  props: {
    dataProvider: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    mode: {
      type: String,
      default: "edit"
    },
    value: {
      type: Object,
      default: () => ({})
    },
    showInitLine: {
      type: Boolean,
      default: false
    },
    isActionCollection: {
      type: Boolean,
      default: false
    },
    isUserGroup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refObj: {},
      _
    };
  },
  watch: {
    value: {
      handler(newValue) {
        if (_.isEmpty(newValue)) {
          if (this.showInitLine) {
            this.onChange({
              connector: "AND",
              filters: [
                {
                  connector: "AND",
                  eventGroup: {}
                }
              ]
            });
          } else {
            this.onChange({
              connector: "AND",
              filters: []
            });
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    // 设置ref引用
    setRef(index, key, el) {
      if (!this.refObj[index]) {
        this.refObj[index] = {};
      }
      this.refObj[index][key] = el;
    },

    // 验证方法
    isValid() {
      let resultArr = [];
      _.forEach(this.refObj, (v) => {
        let values = _.values(v);
        _.forEach(_.without(values, null), (item) => {
          if (item && item.isValid) {
            resultArr.push(item.isValid(true));
          }
        });
      });
      return !resultArr.includes(false);
    },

    onChangeConnector(datas, type) {
      const result = { ...this.value, [type]: datas };
      this.onChange(result);
    },

    onSubConnectorChange(datas, type, data) {
      data[type] = datas;
      this.onChange({ ...this.value });
    },

    count() {
      return (this.value && this.value.filters && this.value.filters.length) || 1;
    },

    handleAddSubItem(type, index) {
      let _value = _.cloneDeep(this.value);
      _value.filters[index][type] = {};
      this.onChange(_value);
    },

    onAddItemFilter(type) {
      let _value = _.cloneDeep(this.value);
      _value.filters.push({
        connector: "AND",
        [type]: {}
      });
      this.onChange(_value);
    },

    onChangeItemFilter(type, v, index, innerValue) {
      const _value = _.cloneDeep(this.value);
      if (_.isEmpty(innerValue.filters)) {
        _value.filters[index] = _.omit(_value.filters[index], type);
        if (
          !_value.filters[index].eventGroup &&
          !_value.filters[index].userProperty &&
          !_value.filters[index].userLabel &&
          !_value.filters[index].segment
        ) {
          _value.filters.splice(index, 1);
        }
      } else {
        _value.filters[index][type] = v;
      }
      this.onChange && this.onChange(_value);
    },

    getItemKeys(item) {
      return _.keys(item).slice(1);
    },

    getOrderedKeys(item) {
      let keys = this.getItemKeys(item);
      // 整理顺序
      let _keys = [];
      _keys[0] = _.find(keys, (key) => key === "eventGroup");
      _keys[1] = _.find(keys, (key) => key === "userProperty");
      _keys[2] = _.find(keys, (key) => key === "userLabel");
      _keys[3] = _.find(keys, (key) => key === "segment");

      return _.without(_keys, false, undefined, null);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
