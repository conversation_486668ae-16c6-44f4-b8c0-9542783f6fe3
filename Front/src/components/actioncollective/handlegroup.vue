<template>
  <div
    v-if="mode !== 'detail'"
    class="action_collective_button_group"
    :style="{ display: mode === 'detail' ? 'none' : 'block' }"
  >
    <!-- fixme 这里要改成boolean -->
    <a-button type="dashed" :disabled="value.eventGroup" @click="() => onClick('eventGroup')">
      <a-icon type="plus" />
      行为
    </a-button>
    <!-- <a-button type="dashed" :disabled="_isEmpty(value.userProperty)" @click="() => onClick('userProperty')">
      <a-icon type="plus" />
      属性
    </a-button> -->
    <a-button type="dashed" :disabled="value.userLabel" @click="() => onClick('userLabel')">
      <a-icon type="plus" />
      标签
    </a-button>
    <a-button type="dashed" :disabled="value.segment" @click="() => onClick('segment')">
      <a-icon type="plus" />
      AI决策模型
    </a-button>
  </div>
</template>

<script>
import lodash from "lodash";

export default {
  name: "HandleGroup",
  props: {
    onClick: {
      type: Function,
      required: true
    },
    mode: {
      type: String,
      default: "edit"
    },
    value: {
      type: Object,
      required: true
    }
  },
  methods: {
    _isEmpty(value) {
      return lodash.isEmpty(value);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
