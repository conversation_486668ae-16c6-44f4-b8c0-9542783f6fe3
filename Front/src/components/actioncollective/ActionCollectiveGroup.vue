<template>
  <div class="FilterGroupPanel3">
    <div class="ConnectorPanel" :style="{ display: filterCount <= 1 ? 'none' : 'block' }">
      <div class="TopLine" />
      <div class="VLine" />
      <div class="BottomLine" />
      <div class="Connector">
        <FilterConnector :mode="mode" :value="connector" :onChange="onChangeConnector" />
      </div>
    </div>
    <ul :class="`FilterList ${inner}`">
      <slot />
    </ul>
  </div>
</template>

<script>
import FilterConnector from "./FilterConnector.vue";

export default {
  name: "ActionCollectiveGroup",
  components: {
    FilterConnector
  },
  props: {
    connector: {
      type: String,
      required: true,
      default: "AND"
    },
    onChangeConnector: {
      type: Function,
      default: () => {}
    },
    filterCount: {
      type: Number,
      required: true
    },
    inner: {
      type: String,
      default: ""
    },
    mode: {
      type: String,
      default: "edit"
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
