import React, { useContext, useEffect, useState, useCallback } from 'react';
import FilterContext from './FilterContext';
import _ from 'lodash';
import { Dropdown, Input } from 'antd';
import { CloseCircleOutlined } from '@ant-design/icons';

interface CalendarComponentProps {
  value: {
    id: string;
    label: string;
    displayName: string;
    operator: string;
    value: string;
    fieldType: string;
    dateType: 'RELATIVE' | 'ABSOLUTE' | 'LATEST';
    showValue: string;
    checkUserTag: boolean;
    exCalendar: {
      [key: number]: [{
        data: string;
      }];
    };
  };
  onChange?: (value: CalendarComponentProps['value']) => void;
  mode?: 'edit' | 'view' |'detail';
}

interface TemplateValue {
  id: number,
  name: string,
  summaryList: [
      {
          headDate: number,
          tailDate: number,
          summary: string
      }
  ]
}

export default function CalendarComponent({ value, onChange, mode = 'edit' }: CalendarComponentProps) {
  const { dataProvider } = useContext(FilterContext);
  const [calendarList, setCalendarList] = useState<TemplateValue[]>([]);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
   (async () => {
      try {
        if (!dataProvider.getCalendar) throw new Error('标签排除日历组件 需要传递dataProvider.getCalendar');
        const _list = await dataProvider.getCalendar([
          {
          operator: 'EQ',
          propertyName: 'status',
          value: 'NORMAL'
        }
      ]);
      setCalendarList(_list);
    } catch (error) {
      console.error(error);
    }
   })();
  }, []);

  const getShowValue = useCallback((calendarList: TemplateValue[]) => {
    const { exCalendar = {} } = value;
    if (_.isEmpty(exCalendar)) return;
    const calendar = calendarList.find((item) => item.id === Number(Object.keys(exCalendar)[0]));
    const key = Object.keys(exCalendar)[0] as any;
    if (!key) return;
    const _showValue = `${calendar?.name! || key} / ${exCalendar[key][0].data}`;
    return _showValue;
  }, [value, calendarList]);

  const updateValue = (exCalendar: CalendarComponentProps['value']['exCalendar']) => {
    const _v = {
      ...value,
      exCalendar
    } as CalendarComponentProps['value'];
    // @ts-ignore
    value.changeExCalendar(_v.exCalendar);
    onChange && onChange(_v);
  };

  if (mode === 'detail') {
    return <span style={{ margin: '0 8px' }}>排除日历：{getShowValue(calendarList)}</span>;
  }

  return <span>
    <span style={{ marginRight: 8 }}>排除</span>
    <Dropdown
      trigger={['click']}
      overlayStyle={{
      width: 200,
      maxHeight: !_.isEmpty(calendarList) ? 200 : 'auto',
      overflowY: !_.isEmpty(calendarList) ? 'auto' : 'hidden',
      overflowX: 'hidden',
      marginTop: 8,
      boxShadow: '0px 6px 16px -8px rgba(0, 0, 0, 0.08), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 12px 48px 16px rgba(0, 0, 0, 0.03)'
    }}
      menu={{
      items: calendarList.reduce((acc: any[], item) => {
        if (item.name.includes(searchValue)) {
          const uniqueSummaries = new Map();
          item?.summaryList?.length > 0 && item.summaryList.forEach((summary) => {
            if (!uniqueSummaries.has(summary.summary)) {
              uniqueSummaries.set(summary.summary, {
                label: summary.summary,
                key: `${item.id}/${summary.summary}/${summary.headDate}/${summary.tailDate}`,
                onClick: () => {
                  const _exCalendar = {
                    [item.id]: [{ data: summary.summary }]
                  };
                  const _v = {
                    ...value,
                    exCalendar: _exCalendar
                  } as CalendarComponentProps['value'];
                  // @ts-ignore
                  value.changeExCalendar(_v.exCalendar);
                  onChange && onChange(_v);
                }
              });
            }
          });
          acc.push({
            label: item.name,
            key: item.id,
            children: Array.from(uniqueSummaries.values())
          });
        }
        return acc;
      }, [])
    }}
    >
      <span className="relative">
        <Input
          title={getShowValue(calendarList) || ''}
          value={getShowValue(calendarList)}
          style={{ height: 32, width: 200, marginRight: 8 }}
          onChange={(e: any) => setSearchValue(e.target.value)}
          placeholder="营销日历/日历标识"
          readOnly
          suffix={
            <CloseCircleOutlined
              style={{ color: 'rgba(0,0,0,0.45)' }}
              onClick={() => updateValue({})}
            />
          }
        />

      </span>
    </Dropdown>
  </span>;
}
