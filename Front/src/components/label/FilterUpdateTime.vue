<template>
  <div>
    <!-- 相对时间 -->
    <div v-if="value.dateType === 'RELATIVE'" style="display: flex; gap: 4px; align-items: center">
      <a-input-number
        :value="timeValue"
        @change="handleTimeChange"
        :min="0"
        style="width: 80px"
      />
      <a-select
        :value="value.timeType || 'DAY'"
        @change="handleTimeTypeChange"
        style="width: 80px"
      >
        <a-select-option value="DAY">天前</a-select-option>
        <a-select-option value="MONTH">月末</a-select-option>
        <a-select-option value="YEAR">年末</a-select-option>
      </a-select>
    </div>
    
    <!-- 绝对时间 -->
    <a-date-picker
      v-else-if="value.dateType === 'ABSOLUTE'"
      :value="absoluteValue"
      @change="handleAbsoluteChange"
      style="width: 100%"
    />
    
    <!-- 最新值 -->
    <span v-else-if="value.dateType === 'LATEST'">
      最新值
    </span>
  </div>
</template>

<script>
import { InputNumber, Select, DatePicker } from "ant-design-vue";
import moment from "moment";

export default {
  name: "LabelFilterUpdateTime",
  components: {
    "a-input-number": InputNumber,
    "a-select": Select,
    "a-select-option": Select.Option,
    "a-date-picker": DatePicker
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    timeValue() {
      if (typeof this.value.times === 'object' && this.value.times !== null) {
        return this.value.times.number;
      }
      return this.value.times;
    },
    
    absoluteValue() {
      return this.value.times ? moment(this.value.times) : null;
    }
  },
  methods: {
    handleTimeChange(val) {
      if (this.value.timeType && this.value.timeType !== 'DAY') {
        this.value.times = {
          number: val,
          timeType: this.value.timeType
        };
      } else {
        this.value.times = val;
      }
      this.onChange(this.value);
    },
    
    handleTimeTypeChange(timeType) {
      this.value.timeType = timeType;
      if (timeType !== 'DAY') {
        this.value.times = {
          number: this.timeValue || 0,
          timeType: timeType
        };
      } else {
        this.value.times = this.timeValue || 0;
      }
      this.onChange(this.value);
    },
    
    handleAbsoluteChange(date) {
      this.value.times = date ? date.valueOf() : null;
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
/* 样式将从label.scss中继承 */
</style>
