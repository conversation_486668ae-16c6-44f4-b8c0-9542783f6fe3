<template>
  <li :class="`FilterSingle ${mode}`">
    <div style="display: flex; flex-direction: column; gap: 8px" :hidden="mode !== 'edit' && !value.valid().isValid">
      <div style="display: flex">
        <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
          <FilterSingleWrapper :value="displayName" :useTakePlaceWidth="true" :info="value">
            <FilterField :value="value" :onChange="onChange" />
          </FilterSingleWrapper>
        </div>
        <div :class="`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`">
          <FilterSingleWrapper :value="value.getOperatorShow()" :useTakePlaceWidth="true">
            <FilterOperator :value="value" :onChange="onChange" />
          </FilterSingleWrapper>
        </div>
        <div
          :class="`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`"
          :hidden="value.isValueCanEdit() === false"
        >
          <FilterSingleWrapper :value="value.getValueShow()">
            <FilterValue :value="value" :onChange="onChange" />
          </FilterSingleWrapper>
        </div>
      </div>

      <div style="display: flex">
        <div style="padding-left: 8px">
          标签更新时间
          <span style="padding: 0 8px">
            <a-tooltip :overlay-style="{ maxWidth: '410px' }" placement="top">
              <template #title>
                <div>
                  相对值：将使用相对的时间作为标签业务数据<br />
                  固定值：将使用固定的时间来作为标签业务数据<br />
                  最新值：将最近一次计算的时间来作为标签业务数据，可选择排除某日历的日历标识，最新值会根据排除的日历标识，自动选取最近的一个计算的日期。
                </div>
              </template>
              <a-icon type="info-circle" />
            </a-tooltip>
          </span>
          ：
        </div>
        <div :class="`FilterOperator ${mode} ${validator?.dateType && value.validating ? 'has-error' : ''}`">
          <FilterSingleWrapper :value="value.getDateTypeShow()" :useTakePlaceWidth="true">
            <FilterTimeType :value="value" :onChange="onChange" />
          </FilterSingleWrapper>
        </div>
        <div
          v-if="value.dateType !== 'LATEST'"
          :class="`FilterValue ${mode} ${validator?.times && value.validating ? 'has-error' : ''}`"
        >
          <FilterSingleWrapper :value="value.getTimeShow()">
            <FilterUpdateTime :value="value" :onChange="onChange" />
          </FilterSingleWrapper>
        </div>

        <span v-if="mode === 'edit' && value.dateType === 'LATEST'">
          <a-input
            :value="getLastSyncDateDisplay()"
            :style="{ height: '32px', width: '108px', marginRight: '8px' }"
            readonly
          />
          <CalendarComponent v-if="isUserGroup" :value="value" :onChange="onChange" />
        </span>

        <div v-if="mode === 'edit'" class="Ctroller">
          <a-tooltip v-if="value.validating && hasValidationError" placement="topRight" :title="getFirstErrorMessage()">
            <div style="margin-right: 5px; display: flex">
              <a-icon type="question-circle" class="Validator" />
            </div>
          </a-tooltip>
          <a-icon type="plus-circle" class="add" @click="onAdd" :hidden="!canAdd" />
          <a-icon type="close-circle" class="delete" @click="onDelete" />
        </div>

        <div style="margin-left: 12px">
          <CalendarComponent
            v-if="isUserGroup && mode === 'detail' && value.dateType === 'LATEST' && !isEmptyObject(value.exCalendar)"
            :value="value"
            mode="detail"
          />
          {{ getLastCalcTimeDisplay() }}
        </div>
      </div>
    </div>

    <FilterRule v-if="checked" :value="value" :onChange="onChange" />
  </li>
</template>

<script>
import { Tooltip, Input } from "ant-design-vue";
import moment from "moment";
import _ from "lodash";
import FilterSingleWrapper from "./FilterSingleWrapper.vue";
import FilterField from "./FilterField.vue";
import FilterOperator from "./FilterOperator.vue";
import FilterValue from "./FilterValue.vue";
import FilterTimeType from "./FilterTimeType.vue";
import FilterUpdateTime from "./FilterUpdateTime.vue";
import FilterRule from "./FilterRule.vue";
import CalendarComponent from "./CalendarComponent.vue";

export default {
  name: "LabelFilterSingle",
  components: {
    "a-tooltip": Tooltip,
    "a-input": Input,
    FilterSingleWrapper,
    FilterField,
    FilterOperator,
    FilterValue,
    FilterTimeType,
    FilterUpdateTime,
    FilterRule,
    CalendarComponent
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    onAdd: {
      type: Function,
      default: () => {}
    },
    onDelete: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      validator: {},
      detail: undefined,
      lastSyncDate: undefined,
      isFirstRender: true,
      _
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    canAdd() {
      return this.context.canAdd;
    },
    mode() {
      return this.context.mode || "edit";
    },
    dataProvider() {
      return this.context.dataProvider;
    },
    checked() {
      return this.context.checked;
    },
    isUserGroup() {
      return this.context.isUserGroup;
    },
    displayName() {
      return this.value.displayName;
    },
    hasValidationError() {
      return this.validator?.id || this.validator?.times || this.validator?.operator || this.validator?.value;
    }
  },
  watch: {
    "value.id": {
      handler() {
        this.updateValidator();
        this.handleValueChange();
      }
    },
    "value.value": {
      handler() {
        this.updateValidator();
      }
    },
    "value.times": {
      handler() {
        this.updateValidator();
      }
    },
    "value.operator": {
      handler() {
        this.updateValidator();
      }
    },
    "value.dateType": {
      handler() {
        this.updateValidator();
        this.handleValueChange();
      }
    },
    "value.checkUserTag": {
      handler() {
        this.updateValidator();
      }
    },
    "value.checkDuration": {
      handler() {
        this.updateValidator();
      }
    },
    "value.timeTerm": {
      handler() {
        this.updateValidator();
      }
    },
    mode: {
      handler() {
        this.updateValidator();
        if (this.mode !== "edit") {
          this.getTagList();
        }
      }
    }
  },
  methods: {
    updateValidator() {
      if (this.mode !== "edit") return;
      this.validator = this.value.valid();
    },

    handleValueChange() {
      if (this.isFirstRender) {
        this.isFirstRender = false;
        return;
      }
      this.value.changeExCalendar({});
      this.onChange && this.onChange(this.value);
    },

    async getTagList() {
      if (this.dataProvider.getLabelInfo) {
        const res = await this.dataProvider.getLabelInfo(this.value.id);
        this.detail = res;
      }
    },

    getFirstErrorMessage() {
      if (this.validator.message && this.validator.message.length > 0) {
        return _.head(_.values(this.validator.message));
      }
      return "";
    },

    getLastSyncDateDisplay() {
      if (_.isNumber(this.lastSyncDate)) {
        return moment(this.lastSyncDate).format("YYYY-MM-DD");
      } else if (this.value?.tagInfo?.busiDate) {
        return moment(this.value.tagInfo.busiDate).format("YYYY-MM-DD");
      }
      return "-";
    },

    getLastCalcTimeDisplay() {
      if (this.mode === "edit") {
        return this.value?.tagInfo?.lastCalcTime
          ? `标签最近计算时间：${moment(this.value.tagInfo.lastCalcTime).format("YYYY-MM-DD HH:mm:ss")}`
          : "标签最近计算时间： -";
      } else {
        return `标签最近计算时间：${
          this.detail && this.detail.lastCalcTime ? moment(this.detail.lastCalcTime).format("YYYY-MM-DD HH:mm:ss") : "-"
        }`;
      }
    }
  },
  mounted() {
    this.updateValidator();
    if (this.mode !== "edit") {
      this.getTagList();
    }
  }
};
</script>

<style scoped>
/* 样式将从label.scss中继承 */
</style>
