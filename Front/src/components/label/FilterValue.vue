<template>
  <div>
    <!-- fixme 这个文件需要和filter 组件一样 用拆分的组件来管理 可以直接抄过来 -->
    <!-- 字符串类型输入 -->
    <a-input v-if="isStringInput" :value="value.value" @change="handleStringChange" placeholder="请输入值" />

    <!-- 数字类型输入 -->
    <a-input-number
      v-else-if="isNumberInput"
      :value="value.value"
      @change="handleNumberChange"
      placeholder="请输入数字"
      style="width: 100%"
    />

    <!-- 多选输入 -->
    <a-select
      v-else-if="isMultiSelect"
      mode="multiple"
      :value="value.value"
      @change="handleMultiSelectChange"
      placeholder="请选择值"
      style="width: 100%"
    >
      <a-select-option v-for="item in tagValues" :key="item.value" :value="item.value">
        {{ item.displayValue || item.value }}
      </a-select-option>
    </a-select>

    <!-- 日期输入 -->
    <a-date-picker v-else-if="isDateInput" :value="dateValue" @change="handleDateChange" style="width: 100%" />

    <!-- 日期时间输入 -->
    <a-date-picker
      v-else-if="isDateTimeInput"
      show-time
      :value="dateValue"
      @change="handleDateChange"
      style="width: 100%"
    />

    <!-- 范围输入 -->
    <div v-else-if="isRangeInput" style="display: flex; gap: 8px">
      <a-input-number
        :value="rangeValue[0]"
        @change="(val) => handleRangeChange(val, 0)"
        placeholder="最小值"
        style="flex: 1"
      />
      <span>~</span>
      <a-input-number
        :value="rangeValue[1]"
        @change="(val) => handleRangeChange(val, 1)"
        placeholder="最大值"
        style="flex: 1"
      />
    </div>

    <!-- 默认输入 -->
    <a-input v-else :value="value.value" @change="handleStringChange" placeholder="请输入值1" />
  </div>
</template>

<script>
import { Input, InputNumber, Select, DatePicker } from "ant-design-vue";
import moment from "moment";

export default {
  name: "LabelFilterValue",
  components: {
    "a-input": Input,
    "a-input-number": InputNumber,
    "a-select": Select,
    "a-select-option": Select.Option,
    "a-date-picker": DatePicker
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      tagValues: []
    };
  },
  computed: {
    isStringInput() {
      const { operator, fieldType } = this.value;
      return (
        ["STRING"].includes(fieldType) &&
        ["EQ", "NE", "LIKE", "NOT_LIKE", "START_WITH", "NOT_START_WITH", "END_WITH", "NOT_END_WITH"].includes(operator)
      );
    },

    isNumberInput() {
      const { operator, fieldType } = this.value;
      return ["INT", "LONG", "DOUBLE"].includes(fieldType) && ["EQ", "NE", "GT", "GTE", "LT", "LTE"].includes(operator);
    },

    isMultiSelect() {
      const { operator } = this.value;
      return ["IN", "NOT_IN"].includes(operator);
    },

    isDateInput() {
      const { operator, fieldType } = this.value;
      return ["DATE", "HIVE_DATE"].includes(fieldType) && ["EQ", "NE", "GT", "GTE", "LT", "LTE"].includes(operator);
    },

    isDateTimeInput() {
      const { operator, fieldType } = this.value;
      return (
        ["DATETIME", "TIMESTAMP", "HIVE_TIMESTAMP"].includes(fieldType) &&
        ["EQ", "NE", "GT", "GTE", "LT", "LTE"].includes(operator)
      );
    },

    isRangeInput() {
      const { operator } = this.value;
      return ["BETWEEN"].includes(operator);
    },

    dateValue() {
      return this.value.value ? moment(this.value.value) : null;
    },

    rangeValue() {
      return Array.isArray(this.value.value) ? this.value.value : [null, null];
    }
  },
  methods: {
    handleStringChange(e) {
      this.value.value = e.target.value;
      this.onChange(this.value);
    },

    handleNumberChange(val) {
      this.value.value = val;
      this.onChange(this.value);
    },

    handleMultiSelectChange(values) {
      this.value.value = values;
      this.onChange(this.value);
    },

    handleDateChange(date) {
      this.value.value = date ? date.valueOf() : null;
      this.onChange(this.value);
    },

    handleRangeChange(val, index) {
      const newRange = [...this.rangeValue];
      newRange[index] = val;
      this.value.value = newRange;
      this.onChange(this.value);
    }
  },
  watch: {
    "value.id": {
      async handler() {
        // 当标签ID变化时，获取标签值
        if (this.value.tagInfo && this.value.tagInfo.userLabelValues) {
          const a =this.value.tagInfo.userLabelValues
          debugger
          this.tagValues = this.value.tagInfo.userLabelValues;
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
/* 样式将从label.scss中继承 */
</style>
