<template>
  <FilterSingleWrapperContextProvider :value="{}">
    <div class="FilterSingleWrapper" :style="style">
      <div v-if="mode === 'detail'" class="valueShow">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'" class="takePlace" ref="takePlace">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'">
        <slot />
      </div>
    </div>
  </FilterSingleWrapperContextProvider>
</template>

<script>
import _ from "lodash";
import { FilterSingleWrapperContextProvider } from "./FilterSingleWrapperContext";

export default {
  name: "LabelFilterSingleWrapper",
  components: {
    FilterSingleWrapperContextProvider
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ""
    },
    useTakePlaceWidth: {
      type: Boolean,
      default: false
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      style: {}
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    mode() {
      return this.context.mode || "edit";
    },
    displayValue() {
      if (!this.value) {
        return "";
      }
      return _.isArray(this.value) ? `[${this.value.join(',')}]` : this.value;
    }
  },
  watch: {
    value: {
      handler() {
        this.updateStyle();
      }
    },
    mode: {
      handler() {
        this.updateStyle();
      }
    }
  },
  methods: {
    updateStyle() {
      this.$nextTick(() => {
        if (this.$refs.takePlace && this.mode === 'edit' && this.useTakePlaceWidth) {
          this.style = { 
            width: Math.max(this.$refs.takePlace.clientWidth, 55) + 45 + 'px'
          };
        } else {
          this.style = {};
        }
      });
    }
  },
  mounted() {
    this.updateStyle();
  }
};
</script>

<style scoped>
/* 样式将从label.scss中继承 */
</style>
