import React, { useState, useContext, useEffect, useRef, Fragment } from 'react';
import { Input, AutoComplete, DatePicker, Select, InputNumber, Checkbox, Tooltip } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import SelectTime from '../selectTime';
import { t } from '../utils/translation';

const { Option } = AutoComplete;
const { RangePicker } = DatePicker;

const FilterValueContext = React.createContext();

// eslint-disable-next-line no-shadow
const monthOption = new Array(12).fill(0).map((_, index) => {
  const month = (index + 1).toString().padStart(2, '0');
  return { value: month, label: `${index + 1}${t('cpnt-frZeanQ0tQbu')}` };
});

// eslint-disable-next-line no-shadow
const dayOptions = new Array(31).fill(0).map((_, index) => {
  const day = (index + 1).toString().padStart(2, '0');
  return { value: day, label: `${index + 1}${t('cpnt-VigOoa0mSlAz')}` };
});

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { items, onChange, fieldShowValue } = props;
  const { setMenuVisible } = useContext(FilterValueContext);
  const inputEl = useRef(null);

  if (_.isArray(items)) {
    items = items.filter(n => !!n.value);
  } else {
    items = [];
  }
  const itemsDs = items.filter(n => !!n.value).map(v => (
    // <Option label={v.value || v} value={v.value || v} key={v.id}>{v.value || v}</Option>
    { value: v.priorityShow === 2 ? v.displayValue ? v.valueAndDisplayValue : v.value : v.value, label: v.priorityShow === 2 ? v.displayValue ? v.valueAndDisplayValue : v.value : v.value }
  ));

  return (
    <AutoComplete
      // dataSource={itemsDs}
      style={{
        width: 170
      }}
      showSearch={items.length > 0}
      onChange={onChange}
      value={fieldShowValue}
      options={itemsDs}
      filterOption={(inputValue, option) => `${option.value}`.toUpperCase().indexOf(typeof inputValue === 'string' ? inputValue.toUpperCase() : '') !== -1}
      // allowClear
      // optionLabelProp="label"
      ref={inputEl}
      onDropdownVisibleChange={v => setMenuVisible(v)}
    />
  );
  // return 'OneInput';
}

function DateInput(props) {
  const { fieldType, onChange } = props;

  const showTime = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? { format: 'HH:mm:ss' } : null;
  const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
  const unit = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'second' : 'day';
  const onValueChange = m => {
    onChange(m.startOf(unit).valueOf());
  };

  return (
    <DatePicker
      placeholder={t('cpnt-iRQaaNMBlOHi')}
      showTime={showTime}
      format={format}
      // allowClear
      allowClear={false}
      value={props.fieldValue && dayjs(props.fieldValue)}
      // getCalendarContainer={triggerNode => triggerNode.parentNode}
      onChange={onValueChange}
    />
  );
}

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldType } = props;
  if (fieldType === 'DATE' || fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_DATE' || fieldType === 'HIVE_TIMESTAMP') {
    return DateInput(props);
  }

  return TextInput(props);
}

const longToMoment = fv => {
  return _.isArray(fv)
    ? fv.map(v => dayjs(v))
    : undefined;
};

/**
 * 范围日历输入框
 */
function DateBetweenInput(props) {
  const { fieldValue, fieldType, onChange } = props;
  const [value, setValue] = useState(longToMoment(fieldValue));
  const showTime = !!(fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP');
  const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
  const unit = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'second' : 'day';
  const onValueChange = m => {
    setValue(m);
    onChange(m && m[0] && m[1] && [m[0].startOf(unit).valueOf(), m[1].startOf(unit).valueOf()]);
  };

  // const onValueOk = m => {
  //   onChange(m && m[0] && m[1] && [m[0].valueOf(), m[1].valueOf()]);
  // };

  return (
    <RangePicker
      allowClear={false}
      showTime={showTime}
      format={format}
      placeholder={[t('cpnt-oAfp0trWN3'), t('cpnt-d3JBsE9UTQ')]}
      // onOk={onValueOk}
      onChange={onValueChange}
      value={value}
    />
  );
}

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }
  return (
    <>
      <InputNumber
        style={{ width: 100, marginRight: '5px' }}
        placeholder={t('cpnt-ENsT9hSQZo')}
        value={fieldValue[0]}
        ref={inputEl}
        onChange={value => !isNaN(value) && onChange([value, fieldValue[1]])}
      />
      {t('cpnt-pe2RuwjyuF')}
      <InputNumber
        style={{ width: 100, marginLeft: '5px' }}
        placeholder={t('cpnt-pUTdoGtApv')}
        value={fieldValue[1]}
        onChange={value => !isNaN(value) && onChange([fieldValue[0], value])}
      />
    </>
  );
}

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  if (fieldType === 'DATE' || fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_DATE' || fieldType === 'HIVE_TIMESTAMP') {
    return (
      <DateBetweenInput
        fieldType={fieldType}
        operator={operator}
        fieldValue={fieldValue}
        items={items}
        onChange={onChange}
      />
    );
  }
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

/**
 * 枚举类型的输入框
 * @param {object} props
 */
function EnumInput(props) {
  let { onChange, fieldValue, items, fieldShowValue } = props;
  const inputEl = useRef(null);
  const [checked, setChecked] = useState(false);
  const { setMenuVisible } = useContext(FilterValueContext);

  // if (!_.isArray(fieldValue)) {
  //   fieldValue = [];
  // }

  if (!_.isArray(fieldShowValue)) {
    fieldShowValue = [];
  }

  useEffect(() => {
    if (checked && !_.isEmpty(fieldValue)) {
      changeSelect(fieldValue);
    }
  }, [checked]);

  const changeSelect = v => {
    if (checked) {
      const mapArr = [',', '，', ' ', '、', ';', '；'];
      const regex = new RegExp(`[${mapArr.join('')}]`, 'g');

      // 每个字符串按照分隔符分割，并平铺成一个单一数组
      let result = v.map(item => item.replace(regex, ',').split(','))
        .reduce((acc, curr) => acc.concat(curr), []);

      result = result.filter(item => item);
      result = _.uniq(result);
      onChange(result);
    } else {
      onChange(v);
    }
  };

  const changeCheckBox = e => {
    if (!e.target.checked) {
      onChange([]);
    }
    setChecked(e.target.checked);
  };

  return (
    <div style={{ display: 'flex', gap: 12 }}>
      <Select
        mode="tags"
        onChange={changeSelect}
        allowClear
        maxTagCount={10}
        value={fieldShowValue}
        ref={inputEl}
        style={{ minWidth: 150, maxWidth: 380 }}
        optionLabelProp="label"
        onDropdownVisibleChange={v => setMenuVisible(v)}
      >
        {items.filter(n => !!n.value).map(item => (
          <Option
            value={item.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
            label={item.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
            key={item.id}
          >
            {item.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value ? item.value : item}
          </Option>
        ))}
      </Select>
      <Checkbox onChange={changeCheckBox} checked={checked} style={{ height: 30, whiteSpace: 'nowrap', display: 'flex', alignItems: 'center' }}>
        <Tooltip
          overlayStyle={{ maxWidth: 350 }}
          title={t('cpnt-PjcLh4mUQY')}
        >
          {t('cpnt-HfCgsLSXVs')}
        </Tooltip>
      </Checkbox>
    </div>
  );
}

function AdvancedBetween(props) {
  const { onChange, fieldValue } = props;
  return <SelectTime showTime data={fieldValue} onChange={onChange} />;
}

/**
 *
 *  @description 此处根据operator判断是否是区间 如果是 用数组方式
 */
const MonthAndDaySelect = props => {
  let { onChange, fieldValue, operator } = props;
  const isBetween = operator === 'MONTH_BETWEEN' || operator === 'DAY_BETWEEN';
  const options = operator.includes('MONTH') ? monthOption : dayOptions;
  const placeholder = operator.includes('MONTH') ? t('cpnt-ueKwDCliJ5') : t('cpnt-xuwPHLq9Hb');

  if (isBetween && !_.isArray(fieldValue)) {
    fieldValue = [];
  }

  const handleFirstSelectChange = value => {
    if (!isNaN(value)) {
      onChange(isBetween ? [value, fieldValue[1]] : value);
    } else {
      onChange([undefined, fieldValue[1]]);
    }
  };

  const handleSecondSelectChange = value => {
    if (!isNaN(value)) {
      onChange([fieldValue[0], value]);
    } else {
      onChange([fieldValue[0], undefined]);
    }
  };

  return <div style={{ display: 'flex', gap: 4 }}>
    <Select
      style={{ width: 100 }}
      options={options}
      placeholder={placeholder}
      value={isBetween ? fieldValue[0] : fieldValue}
      onChange={handleFirstSelectChange}
      allowClear
      showSearch
    />
    {isBetween && <>
      <span>~</span>
      <Select
        style={{ width: 100 }}
        options={options}
        placeholder={placeholder}
        value={fieldValue[1]}
        onChange={handleSecondSelectChange}
        allowClear
        showSearch
      />
    </>}
  </div>;
};

const MonthDaySelect = props => {
  let { onChange, fieldValue, operator } = props;
  const isBetween = operator === 'DATE_FORMAT_BETWEEN';
  const isEQ = operator === 'DATE_FORMAT_EQ';

  if (isBetween && !_.isArray(fieldValue)) {
    fieldValue = [];
  }

  // todo 这个方法字符串拆分为月份和日期
  const splitValue = value => (_.isString(value) ? value.split('-') : [undefined, undefined]);

  // 当字段值是数组时，分别处理起始和结束的月日
  const [start, end] = isBetween ? fieldValue : [fieldValue, undefined];
  const [startMonth, startDay] = splitValue(start);
  const [endMonth, endDay] = splitValue(end);

  const handleSelectChange = (part, value, index) => {
    let newStartMonth = startMonth || undefined;
    let newStartDay = startDay || undefined;
    let newEndMonth = endMonth || undefined;
    let newEndDay = endDay || undefined;

    // 根据index确定更改的是哪一部分
    if (index === 0) {
      newStartMonth = value;
    } else if (index === 1) {
      newStartDay = value;
    } else if (index === 2) {
      newEndMonth = value;
    } else if (index === 3) {
      newEndDay = value;
    }
    // 根据操作类型调用onChange回调
    if (isBetween) {
      onChange([`${newStartMonth}-${newStartDay}`, `${newEndMonth}-${newEndDay}`]);
    } else if (isEQ) {
      onChange(`${newStartMonth}-${newStartDay}`);
    }
  };

  const renderSelect = (index, part) => {
    let value = '';
    if (index === 0) value = startMonth;
    else if (index === 1) value = startDay;
    else if (index === 2) value = endMonth;
    else if (index === 3) value = endDay;
    return (
      <Select
        style={{ width: 100 }}
        options={part === 'month' ? monthOption : dayOptions}
        placeholder={part === 'month' ? t('cpnt-ueKwDCliJ5') : t('cpnt-xuwPHLq9Hb')}
        value={value ? value.includes('undefined') ? undefined : value : value}
        onChange={v => handleSelectChange(part, v, index)}
        allowClear
        showSearch
      />
    );
  };

  return (
    <div style={{ display: 'flex', gap: 4 }}>
      {renderSelect(0, 'month')}
      {renderSelect(1, 'day')}
      {isBetween && (
        <>
          <span>~</span>
          {renderSelect(2, 'month')}
          {renderSelect(3, 'day')}
        </>
      )}
    </div>
  );
};

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  const { logProvider, dataProvider } = useContext(FilterContext);
  const [items, setItems] = useState([]);
  const [log] = useState(logProvider.getLogger('FilterValue'));
  const [menuVisible, setMenuVisible] = useState(false);
  const [filterValueContext] = useState({
    setMenuVisible
  });

  // const { field, fieldType, operator, isEnum, id, schemaId } = value;
  const { fieldType, operator, id } = value;
  const [fieldValue, setFieldValue] = useState(value.value);
  const [fieldShowValue, setFieldShowValue] = useState(value.showValue);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    const fetchItem = async () => {
      setItems([]);
      // if (!isEnum) {
      //   return;
      // }
      const _items = await dataProvider.getTagValuesById(id);
      log.debug('fetchItem', _items);
      setItems(_items);
    };
    if (menuVisible) {
      fetchItem();
    }
  }, [id, menuVisible]);

  useEffect(() => {
    setFieldValue(value.value);
  }, [value.value]);

  useEffect(() => {
    setFieldShowValue(value.showValue);
  }, [value.showValue]);

  useEffect(() => {
    log.debug('debounceFieldValue changed call onChange', debounceFieldValue);
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = v => {
    log.debug('onChangeFieldValue', v, JSON.stringify(value));
    value.value = v;
    value.showValue = v;
    // 如果value是个枚举类型的属性，并且枚举是keyvalue形式的，需要向filterModel赋值一个showValue，方便用于回显
    // if (value.isEnum && _.isArray(items) && items.length > 0 && items[0].name) {
    //   const itemValueMap = items.reduce((a, b) => {
    //     a[b.value] = b.name;
    //     return a;
    //   }, {});
    //   if (_.isArray(v)) {
    //     value.showValue = v.map(_v => itemValueMap[_v] || _v);
    //   } else {
    //     value.showValue = itemValueMap[v] || v;
    //   }
    // }
    setFieldValue(v);
    setFieldShowValue(value.showValue);
  };

  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    const props = {
      fieldType,
      operator,
      fieldValue,
      items,
      onChange: onChangeFieldValue
    };
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            fieldShowValue={fieldShowValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'ADVANCED_BETWEEN':
        return (
          <AdvancedBetween
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IN':
      case 'NOT_IN':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <EnumInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            fieldShowValue={fieldShowValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
      case 'ALL':
        return <span />;
      case 'MONTH_BETWEEN':
      case 'MONTH_EQ':
      case 'DAY_BETWEEN':
      case 'DAY_EQ':
        return <MonthAndDaySelect {...props} />;
      case 'DATE_FORMAT_BETWEEN':
      case 'DATE_FORMAT_EQ':
        return <MonthDaySelect {...props} />;
      default:
        return <Input placeholder={t('cpnt-3xbmQNoSsK')} disabled={!operator} />;
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
