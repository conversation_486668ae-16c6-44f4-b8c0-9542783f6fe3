<template>
  <div style="height: 400px; overflow-y: scroll">
    <a-menu :force-sub-menu-render="true">
      <a-menu-item v-if="fetching" key="loading">
        <sync-outlined :spin="true" />
        loading
      </a-menu-item>
      <template v-else>
        <template v-for="(children, level1) in propertyMap" :key="level1">
          <a-menu-item-group v-if="level1" :title="level1">
            <template v-for="(level2Children, level2) in children" :key="level2">
              <a-menu-item-group v-if="level2" :title="level2">
                <a-menu-item
                  v-for="(property, index) in getFilteredProperties(level2Children)"
                  :key="`${level1}-${level2}-${index}`"
                  @click="() => onChange(property)"
                >
                  {{ property.field }}[{{ property.fieldName }}]
                </a-menu-item>
              </a-menu-item-group>
              <a-menu-item
                v-else
                v-for="(property, index) in getFilteredProperties(level2Children)"
                :key="`${level1}-${index}`"
                @click="() => onChange(property)"
              >
                {{ property.field }}[{{ property.fieldName }}]
              </a-menu-item>
            </template>
          </a-menu-item-group>
          <a-menu-item
            v-else
            v-for="(property, index) in getFilteredProperties(children)"
            :key="index"
            @click="() => onChange(property)"
          >
            {{ property.field }}[{{ property.fieldName }}]
          </a-menu-item>
        </template>
      </template>
    </a-menu>
  </div>
</template>

<script>
import { Menu } from "ant-design-vue";
import { SyncOutlined } from "@ant-design/icons-vue";
import _ from "lodash";
import Log from "../utils/log";

const log = Log.getLogger("FilterFieldMenu");

export default {
  name: "FilterFieldMenu",
  components: {
    "a-menu": Menu,
    "a-menu-item": Menu.Item,
    "a-menu-item-group": Menu.ItemGroup,
    "sync-outlined": SyncOutlined
  },
  props: {
    searchText: {
      type: String,
      default: ""
    },
    dataProvider: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    value: {
      type: [String, Object],
      default: null
    }
  },
  data() {
    return {
      propertyMap: {},
      fetching: true
    };
  },
  watch: {
    searchText() {
      this.fetchPropertyList();
    },
    dataProvider() {
      this.fetchPropertyList();
    }
  },
  methods: {
    async fetchPropertyList() {
      this.fetching = true;
      try {
        const plist = await this.dataProvider.getPropertyList();
        let levelMap = _.groupBy(plist, (v) => v.level1);
        _.keys(levelMap).forEach((level1) => {
          levelMap[level1] = _.groupBy(levelMap[level1], (v) => v.level2);
        });
        this.propertyMap = levelMap;
      } catch (error) {
        console.error("Failed to fetch property list:", error);
        this.propertyMap = {};
      } finally {
        this.fetching = false;
      }
    },

    getFilteredProperties(properties) {
      if (!_.isArray(properties)) return [];
      return properties.filter(
        (v) => !this.searchText || v.field.indexOf(this.searchText) >= 0 || v.fieldName.indexOf(this.searchText) >= 0
      );
    }
  },
  mounted() {
    log.debug("before render", this.value);
    this.fetchPropertyList();
  }
};
</script>

<style scoped>
/* 样式继承自父组件 */
</style>
