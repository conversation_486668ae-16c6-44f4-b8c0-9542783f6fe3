<template>
  <FilterContextProvider :value="context">
    <div
      :class="`wolf-static-component_filter_FilterGroupPanel ${className || ''}`"
      :style="{
        display: hideInit && hideAdd && _.isEmpty(currentValue || currentValue.filters) ? 'none' : 'block'
      }"
    >
      <FilterListGroup :value="currentValue" @change="onValueChange" />
      <div v-if="!hideAdd" class="FilterAdder">
        <a-button
          type="dashed"
          :disabled="!context.canAdd"
          :style="{
            display: context.mode === 'detail' ? 'none' : 'inline-block'
          }"
          @click="addFilterGroup"
        >
          + {{ addButtonText }}
        </a-button>
        <span
          :style="{
            marginLeft: '10px',
            display: context.mode === 'detail' ? 'none' : 'inline'
          }"
        >
          [{{ context.filterCount }}/{{ maxFilterCount }}] 最多添加{{ maxFilterCount }}条
        </span>
      </div>
    </div>
  </FilterContextProvider>
</template>

<script>
import { Button } from "ant-design-vue";
import _ from "lodash";
import { FilterContextProvider } from "./FilterContext";
import FilterListGroup from "./FilterListGroup.vue";
import FilterModelUtil from "./FilterModelUtil";
import FilterConfig from "./FilterConfig";
import Log from "../utils/log";

const log = Log.getLogger("Filter");

export default {
  name: "Filter",
  components: {
    "a-button": Button,
    FilterContextProvider,
    FilterListGroup
  },
  props: {
    dataProvider: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    mode: {
      type: String,
      default: "edit"
    },
    hideAdd: {
      type: Boolean,
      default: false
    },
    hideInit: {
      type: Boolean,
      default: false
    },
    className: {
      type: String,
      default: ""
    },
    addButtonText: {
      type: String,
      default: "添加过滤"
    },
    isUserGroup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const { maxFilterCount } = FilterConfig;
    return {
      maxFilterCount,
      currentValue: this.initValue(),
      propsValue: {}
    };
  },
  computed: {
    context() {
      let filterCount = 0;
      if (this.currentValue && this.currentValue.filters) {
        filterCount = this.currentValue.filters.map((v) => v.filters?.length || 0).reduce((a, b) => a + b, 0);
      }
      console.log("this.dataProvider", this.dataProvider.getPropertyList());
      return {
        dataProvider: this.dataProvider,
        logProvider: { getLogger: () => ({ debug: console.log }) },
        canAdd: filterCount < this.maxFilterCount,
        mode: this.mode || "edit",
        filterCount,
        validating: false,
        hideAdd: this.hideAdd,
        hideInit: this.hideInit,
        isUserGroup: this.isUserGroup
      };
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if ((!_.isEmpty(newValue) || !_.isEmpty(this.propsValue)) && !_.isEqual(newValue, this.propsValue)) {
          this.currentValue = this.initValue();
        }
      },
      deep: true
    },
    mode: {
      handler(newVal) {
        console.log("mode 变化了:", newVal);
      }
    }
  },
  methods: {
    initValue() {
      return this.value && this.value.filters && this.value.filters.length > 0
        ? this.value
        : { connector: "AND", filters: [] };
    },

    addFilterGroup() {
      let _value = _.isEmpty(this.currentValue)
        ? {
            connector: "AND",
            filters: []
          }
        : this.currentValue;

      FilterModelUtil.addFilterGroupWithOneFilter(_value);
      this.onValueChange(_value);
      return _value;
    },

    deleteFilter(groupIndex, filterIndex) {
      const _value = { ...this.currentValue };
      if (_value.filters[groupIndex] && _value.filters[groupIndex].filters) {
        _value.filters[groupIndex].filters.splice(filterIndex, 1);

        // 如果过滤组为空，删除整个组
        if (_value.filters[groupIndex].filters.length === 0) {
          _value.filters.splice(groupIndex, 1);
        }
      }
      this.onValueChange(_value);
    },

    onValueChange(v) {
      log.debug("onValueChanged", JSON.stringify(v));
      const _v = FilterModelUtil.getValidJson(v);
      this.propsValue = _v;
      this.onChange && this.onChange(_v, v);
      this.currentValue = { ...v };
    },

    isValid(flag) {
      Object.assign(this.context, { ...this.context, validating: true });
      return FilterModelUtil.isFilterListGroupValid(this.currentValue, flag);
    },

    getValue() {
      return this.currentValue;
    },

    getFilterCount() {
      if (!_.isEmpty(this.currentValue.filters)) {
        return this.currentValue.filters.map((v) => v.filters?.length || 0).reduce((a, b) => a + b, 0);
      }
      return 0;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./filter.scss";
</style>
