<template>
  <div class="FilterGroupPanel">
    <div class="ConnectorPanel" :style="{ display: filterCount <= 1 ? 'none' : 'block' }">
      <div class="TopLine" />
      <div class="VLine" />
      <div class="BottomLine" />
      <div class="Connector">
        <span @click="onChangeImg" style="cursor: pointer">
          <img :src="connector === 'OR' ? img1 : img2" style="width: 30px; margin-left: 5.5px" alt="" />
        </span>
      </div>
    </div>
    <ul :class="`FilterList ${inner}`">
      <slot />
    </ul>
  </div>
</template>

<script>
import { img1, img2 } from "../base64/index";

export default {
  name: "ComplexGroup",
  props: {
    connector: {
      type: String,
      default: "AND"
    },
    onChangeConnector: {
      type: Function,
      default: () => {}
    },
    filterCount: {
      type: Number,
      default: 0
    },
    inner: {
      type: String,
      default: ""
    },
    mode: {
      type: String,
      default: "edit"
    }
  },
  data() {
    return {
      img1,
      img2
    };
  },
  methods: {
    onChangeImg() {
      if (this.mode === "detail") return;
      if (this.connector === "AND") {
        this.onChangeConnector("OR");
      } else {
        this.onChangeConnector("AND");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.FilterGroupPanel {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;

  .ConnectorPanel {
    width: 40px;
    min-width: 40px;
    position: relative;

    .VLine {
      position: absolute;
      top: 15px;
      bottom: 15px;
      left: 50%;
      width: 0px;
      border-left: 1px dotted #ccc;
      z-index: 100;
    }

    .TopLine {
      position: absolute;
      top: 15px;
      left: 50%;
      height: 0;
      width: 50%;
      border-top: 1px dotted #ccc;
      z-index: 100;
    }

    .BottomLine {
      position: absolute;
      bottom: 15px;
      left: 50%;
      height: 0;
      width: 50%;
      border-top: 1px dotted #ccc;
      z-index: 100;
    }

    .Connector {
      position: absolute;
      left: 0;
      top: 50%;
      height: 30px;
      line-height: 30px;
      transform: translateY(-15px);
      vertical-align: middle;
      z-index: 200;
    }
  }

  .FilterList {
    flex: auto;
    margin: 0;
    padding: 0;
    margin: 2px 0;

    &.inner {
      background-color: #fafafa;
    }

    :deep(> li) {
      margin: 2px 2px;
      list-style: none;
    }
  }
}
</style>
