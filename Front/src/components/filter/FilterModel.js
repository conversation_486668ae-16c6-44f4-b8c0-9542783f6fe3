import _ from "lodash";
import moment from "moment";
import FilterConfig from "./FilterConfig";
import Log from "../utils/log";
import { getString } from "../selectTime/config";

const { operatorList, validator, typeFormatter, userGroupOperatorList } = FilterConfig;

const log = Log.getLogger("FilterModel");

class FilterModel {
  static counter = 0;

  static get OPERATOR_MAP() {
    const _operatorList = location.href.includes("userGroup") ? userGroupOperatorList : operatorList;
    return _operatorList.reduce((map, obj) => {
      map[obj.operator] = obj;
      return map;
    }, {});
  }

  constructor(tableId, schemaId, field, fieldName, fieldType, level1, level2, operator, value, showValue, isEnum) {
    this.tableId = tableId;
    this.schemaId = schemaId;
    this.field = field;
    this.fieldName = fieldName;
    this.fieldType = fieldType;
    this.level1 = level1;
    this.level2 = level2;
    this.operator = operator;
    this.value = value;
    this.showValue = showValue;
    this.isEnum = isEnum;
    this.isDirty = false;
    log.debug("constructor", JSON.stringify(this));
  }

  static fromJson({
    tableId,
    schemaId,
    field,
    fieldName,
    fieldType,
    level1,
    level2,
    operator,
    value,
    showValue,
    isEnum
  }) {
    log.debug(
      "fromJson",
      JSON.stringify({
        tableId,
        schemaId,
        field,
        fieldName,
        fieldType,
        level1,
        level2,
        operator,
        value,
        showValue,
        isEnum
      })
    );
    return new FilterModel(
      tableId,
      schemaId,
      field,
      fieldName,
      fieldType,
      level1,
      level2,
      operator,
      value,
      showValue,
      isEnum
    );
  }

  toJson() {
    const { tableId, schemaId, field, fieldName, fieldType, level1, level2, operator, value, showValue, isEnum } = this;
    return {
      tableId,
      schemaId,
      field,
      fieldName,
      fieldType,
      level1,
      level2,
      operator,
      value,
      showValue,
      isEnum
    };
  }

  setDirty(isDirty) {
    this.isDirty = isDirty;
  }

  /**
   * 校验filterModel，返回{
   *  isValid: (boolean) 是否有效
   *  fieldType: 表示fieldType有错误，次值为错误返回值
   *  operator: 同上
   *  value: {
   *    maxLen: 错误信息
   *    required: 错误信息
   *  }
   * }
   */
  valid() {
    // log.debug('__validate', type, value, operator)2;
    const { fieldType, value, operator } = this;

    const result = { isValid: true };
    if (!fieldType) {
      result.fieldType = "请选择过滤属性";
    }

    if (!operator) {
      result.operator = "请输入操作符";
    }
    switch (operator) {
      case "IS_NULL":
      case "IS_NOT_NULL":
      case "IS_TRUE":
      case "IS_FALSE":
        return result;
      default:
    }

    const typeValidator = validator[fieldType];
    if (!typeValidator) {
      if (fieldType) {
        log.info("没有找到对应的校验器", `fieldType=${fieldType}`);
      }
      result.isValid = false;
      const newvalue = {};
      if (result.fieldType) newvalue.fieldType = result.fieldType;
      if (result.operator) newvalue.operator = result.operator;
      result.value = newvalue;
      return result;
    }
    if (_.isArray(value)) {
      // 数组类型返回验证器, 如果有错误的，返回一个

      if (value.length) {
        const valueValidators = value.map((v) => this._typeValueValid(typeValidator, v)).filter((v) => v !== null);
        if (valueValidators.length > 0) {
          result.value = valueValidators[0];
        }
      } else {
        result.value = { required: "请输入" };
      }
    } else {
      result.value = this._typeValueValid(typeValidator, value);
    }

    if (!result.value && !result.operator && !result.fieldType) {
      result.isValid = true;
    } else {
      result.isValid = false;
    }

    if (_.isString(value)) {
      const pre = /^\s+/g;
      const suffix = /\s+$/g;
      if (pre.test(value) || suffix.test(value)) {
        result.value = "值前后不能有空格";
        result.isValid = false;
      }
    }

    log.debug("validate", result);
    return result;
  }

  /**
   * 类型验证器，可以验证STRING, LONG , DOUBLE等数据类型
   * @param {object} typeValidator {required, minLen, maxLen, regex, min, max}
   * @param {object} value 被验证的值
   */
  _typeValueValid(typeValidator, value) {
    const { required, minLen, maxLen, regex, min, max } = typeValidator.option;
    const errMessage = typeValidator.message;

    const message = {};

    if (
      required &&
      (value === null || value === undefined || value === "" || (Array.isArray(value) && value.length === 0))
    ) {
      message.required = errMessage.required;
    }

    if (minLen !== null || minLen !== undefined) {
      if (value !== null && value !== undefined && value !== "" && value.toString().length < minLen) {
        message.minLen = errMessage.minLen;
      }
    }

    if (maxLen !== null || maxLen !== undefined) {
      if (value !== null && value !== undefined && value !== "" && value.toString().length > maxLen) {
        message.maxLen = errMessage.maxLen;
      }
    }

    if (regex && !new RegExp(regex).test(value)) {
      message.regex = errMessage.regex;
    }

    if (min !== null && min !== undefined && Number.parseInt(value) < min) {
      message.min = errMessage.min;
    }

    if (max !== null && max !== undefined && Number.parseInt(value) > max) {
      message.max = errMessage.max;
    }

    log.debug("_typeValueValid", typeValidator, value, message);

    if (_.isEmpty(message)) {
      return null;
    }

    return message;
  }

  changeProperty(property) {
    log.debug("changeProperty", property);
    this.tableId = property.tableId;
    this.schemaId = property.schemaId;
    this.fieldType = property.fieldType;
    this.field = property.field;
    this.fieldName = property.fieldName;
    this.level1 = property.level1;
    this.level2 = property.level2;
    this.isEnum = property.isEnum;
    this.operator = null;
    this.isDirty = false;
    this.value = null;
    this.showValue = null;
  }

  changeOperator(operator) {
    log.debug("changeOperator", operator);
    this.operator = operator;
    this.value = null;
    this.showValue = null;
    this.isDirty = false;
  }

  clearProperty() {
    log.debug("clearProperty");
    this.fieldType = null;
    this.field = null;
    this.fieldName = null;
    this.level1 = null;
    this.level2 = null;
    this.isDirty = false;
  }

  /**
   *
   * @description 获取时间切片名称
   */
  getTimeslicingName = () => {
    return FilterModel.OPERATOR_MAP?.TIMESLICING?.children.find((v) => v?.operator === this?.operator)?.name || "";
  };

  getOperatorShow() {
    return FilterModel.OPERATOR_MAP[this.operator]?.name || this.getTimeslicingName();
  }

  getValueShow() {
    const { value, operator, fieldType, showValue } = this;

    if (!value) {
      return "";
    }

    switch (operator) {
      case "EQ":
      case "NE":
      case "GT":
      case "GTE":
      case "LT":
      case "LTE":
        if (["DATE", "DATETIME", "TIMESTAMP", "HIVE_DATE", "HIVE_TIMESTAMP"].indexOf(fieldType) >= 0) {
          return `${moment(value).format(typeFormatter[fieldType])}`;
        }
        return showValue || value;
      case "BETWEEN":
        if (["DATE", "DATETIME", "TIMESTAMP", "HIVE_DATE", "HIVE_TIMESTAMP"].indexOf(fieldType) >= 0) {
          return `[${moment(value[0]).format(typeFormatter[fieldType])} ~ ${moment(value[1]).format(
            typeFormatter[fieldType]
          )}]`;
        }

        return `[${value[0]}-${value[1]}]`;
      case "ADVANCED_BETWEEN":
        return `[${getString(value[0], true)} - ${getString(value[1], true)}]`;
      case "IN":
      case "NOT_IN":
        return `[${_.join(showValue || value, ",")}]`;
      case "LIKE":
      case "NOT_LIKE":
      case "START_WITH":
      case "NOT_START_WITH":
      case "END_WITH":
      case "NOT_END_WITH":
        return showValue || value;
      case "IS_NOT_NULL":
      case "IS_NULL":
        return "";
      case "MONTH_EQ":
        return `${showValue || value}月`;
      case "DAY_EQ":
        return `${showValue || value}日`;
      case "DATE_FORMAT_EQ":
        return `${showValue || value}日`;
      case "MONTH_BETWEEN":
        return `[${value[0]}月 ~ ${value[1]}月]`;
      case "DAY_BETWEEN":
        return `[${value[0]}日 ~ ${value[1]}日]`;
      case "DATE_FORMAT_BETWEEN":
        return `[${value[0]}日 ~ ${value[1]}日]`;
      default:
        return "";
    }
  }

  isValueCanEdit() {
    switch (this.operator) {
      case "IS_NOT_NULL":
      case "IS_NULL":
      case "IS_TRUE":
      case "IS_FALSE":
        return false;
      default:
        return true;
    }
  }
}

export default FilterModel;
