<template>
  <li :class="`FilterSingle ${mode}`">
    <div
      style="display: flex"
      :style="{
        display: mode !== 'edit' && !value.valid().isValid ? 'none' : 'flex'
      }"
    >
      <div :class="`FilterField ${mode} ${validator?.fieldType && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="fieldName" :useTakePlaceWidth="true">
          <FilterField :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>
      <div :class="`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="value.getOperatorShow()" :useTakePlaceWidth="true">
          <FilterOperator :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>
      <div
        :class="`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`"
        :style="{
          display: value.isValueCanEdit() === false ? 'none' : 'block'
        }"
      >
        <FilterSingleWrapper :value="value.getValueShow()">
          <FilterValue :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip
          v-if="value.validating && (validator?.fieldType || validator?.operator || _.head(_.values(validator.value)))"
          placement="topRight"
          :title="_.values(validator.value)"
        >
          <div style="margin-right: 5px">
            <span class="Validator">?</span>
          </div>
        </a-tooltip>
        <a-icon
          type="plus-circle"
          v-if="!hideAdd"
          class="add"
          @click="onAdd"
          :style="{ display: !canAdd ? 'none' : 'inline-block', marginRight: '5px' }"
        />
        <a-icon type="close-circle" @click="onDeleteItem" />
      </div>
    </div>
  </li>
</template>

<script>
import { Tooltip } from "ant-design-vue";
import _ from "lodash";
import FilterSingleWrapper from "./FilterSingleWrapper.vue";
import FilterField from "./FilterField.vue";
import FilterOperator from "./FilterOperator.vue";
import FilterValue from "./FilterValue.vue";

export default {
  name: "FilterSingle",
  components: {
    "a-tooltip": Tooltip,
    FilterSingleWrapper,
    FilterField,
    FilterOperator,
    FilterValue
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    onAdd: {
      type: Function,
      default: () => {}
    },
    onDelete: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      validator: {}
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    // todo React 写法 const { logProvider, canAdd, mode, hideAdd, hideInit } = useContext(FilterContext); 转成vue就是下方写法
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log })
        }
      );
    },
    canAdd() {
      return this.context.canAdd || false;
    },
    mode() {
      return this.context.mode || "edit";
    },
    hideAdd() {
      return this.context.hideAdd || false;
    },
    hideInit() {
      return this.context.hideInit || false;
    },
    fieldName() {
      return this.value.fieldName;
    }
  },
  watch: {
    value: {
      handler() {
        console.log("FilterSingle value的watch", this.value);
        if (this.mode !== "edit") return;
        // 更新校验器
        this.validator = this.value.valid ? this.value.valid() : {};
      },
      deep: true
    }
  },
  methods: {
    onDeleteItem() {
      this.onDelete && this.onDelete(this.hideInit);
    }
  },
  mounted() {
    const log = this.logProvider.getLogger("FilterSingle");
    log.debug("Before Render", JSON.stringify(this.value));
  }
};
</script>

<style scoped>
/* 样式将从filter.scss中继承 */
</style>
