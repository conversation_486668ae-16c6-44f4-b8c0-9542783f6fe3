<template>
  <div>
    <a-date-picker :allowClear="false" :format="format" :value="momentValue" @change="onValueChange" />
  </div>
</template>

<script>
import moment from "moment";

export default {
  name: "DateInput",
  props: {
    fieldType: {
      type: String,
      required: true
    },
    fieldValue: {
      type: Number,
      default: null
    },
    onChange: {
      type: Function,
      required: true
    }
  },

  computed: {
    showTime() {
      return this.fieldType === "DATETIME" || this.fieldType === "TIMESTAMP" || this.fieldType === "HIVE_TIMESTAMP"
        ? { format: "HH:mm:ss" }
        : false;
    },
    format() {
      return this.fieldType === "DATETIME" || this.fieldType === "TIMESTAMP" || this.fieldType === "HIVE_TIMESTAMP"
        ? "YYYY-MM-DD HH:mm:ss"
        : "YYYY-MM-DD";
    },
    unit() {
      return this.fieldType === "DATETIME" || this.fieldType === "TIMESTAMP" || this.fieldType === "HIVE_TIMESTAMP"
        ? "second"
        : "day";
    },
    momentValue() {
      return this.fieldValue && moment(this.fieldValue);
    }
  },
  methods: {
    onValueChange(m) {
      this.onChange(m.startOf(this.unit).valueOf());
    },
    moment
  }
};
</script>

<style scoped>
/* DateInput 特定样式 */
</style>
