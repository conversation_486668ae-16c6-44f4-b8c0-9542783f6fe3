<template>
  <a-range-picker
    :allowClear="false"
    :showTime="showTime"
    :format="format"
    :placeholder="placeholders"
    @change="onValueChange"
    :value="value"
  />
</template>

<script>
import moment from "moment";
import _ from "lodash";

export default {
  name: "DateBetweenInput",
  props: {
    fieldType: {
      type: String,
      required: true
    },
    fieldValue: {
      type: Array,
      default: () => []
    },
    onChange: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      value: this.longToMoment(this.fieldValue)
    };
  },
  computed: {
    showTime() {
      return !!(this.fieldType === "DATETIME" || this.fieldType === "TIMESTAMP" || this.fieldType === "HIVE_TIMESTAMP");
    },
    format() {
      return this.fieldType === "DATETIME" || this.fieldType === "TIMESTAMP" || this.fieldType === "HIVE_TIMESTAMP"
        ? "YYYY-MM-DD HH:mm:ss"
        : "YYYY-MM-DD";
    },
    unit() {
      return this.fieldType === "DATETIME" || this.fieldType === "TIMESTAMP" || this.fieldType === "HIVE_TIMESTAMP"
        ? "second"
        : "day";
    },
    placeholders() {
      return ["开始时间", "结束时间"];
    }
  },
  watch: {
    fieldValue(newValue) {
      this.value = this.longToMoment(newValue);
    }
  },
  methods: {
    longToMoment(fv) {
      return _.isArray(fv) ? fv.map((v) => moment(v)) : undefined;
    },
    onValueChange(m) {
      this.value = m;
      this.onChange(m && m[0] && m[1] && [m[0].startOf(this.unit).valueOf(), m[1].startOf(this.unit).valueOf()]);
    }
  }
};
</script>

<style scoped>
/* DateBetweenInput 特定样式 */
</style>
