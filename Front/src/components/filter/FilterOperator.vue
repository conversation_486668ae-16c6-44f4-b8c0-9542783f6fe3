<template>
  <div class="FilterOperator">
    <a-dropdown
      :trigger="['click']"
      :visible="menuVisible"
      @visibleChange="handleMenuVisible"
      :getPopupContainer="getPopupContainer"
    >
      <div class="clickWrapper">
        <a-input
          class="ant-dropdown-link"
          placeholder="操作符"
          :value="searchText"
          @input="handleSearchInput"
          @focus="handleFocus"
        />
        <a-icon type="down" style="color: rgba(0, 0, 0, 0.45); font-size: 12px" />
      </div>

      <a-menu
        slot="overlay"
        :forceSubMenuRender="true"
        :style="{
          maxHeight: '400px',
          overflowY: 'auto',
          maxWidth: '500px',
          overflowX: 'auto'
        }"
      >
        <template v-if="value.fieldType">
          <template v-for="(operator, i) in filteredOperators">
            <a-sub-menu v-if="operator.operator === 'TIMESLICING'" :key="i" :title="operator.name">
              <a-menu-item v-for="(child, j) in operator.children" :key="j" @click="() => onSelect(child)">
                {{ child.name }}
              </a-menu-item>
            </a-sub-menu>
            <a-menu-item v-else :key="i" @click="() => onSelect(operator)">
              {{ operator.name }}
            </a-menu-item>
          </template>
        </template>
      </a-menu>
    </a-dropdown>
  </div>
</template>

<script>
import FilterConfig from "./FilterConfig";

const { operatorList, userGroupOperatorList, typeOperator, userGroupTypeOperator } = FilterConfig;

export default {
  name: "FilterOperator",
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      required: true
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      searchText: "",
      menuVisible: false
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    isUserGroup() {
      return this.context.isUserGroup || false;
    },
    OPERATOR_MAP() {
      const list = this.isUserGroup ? userGroupOperatorList : operatorList;
      return list.reduce((map, obj) => {
        map[obj.operator] = obj;
        return map;
      }, {});
    },
    typeOperators() {
      const operators = this.isUserGroup ? userGroupTypeOperator : typeOperator;
      return operators[this.value.fieldType || "STRING"] || [];
    },
    filteredOperators() {
      const menuList = this.typeOperators
        .map((v) => this.OPERATOR_MAP[v])
        .filter((v) => !this.searchText || v?.name.indexOf(this.searchText) >= 0)
        .filter((v) => v);
      return menuList;
    }
  },
  watch: {
    "value.operator": {
      handler() {
        this.updateSearchText();
      },
      immediate: true
    },
    menuVisible(newValue) {
      if (newValue) {
        this.searchText = "";
      } else {
        this.updateSearchText();
      }
    }
  },
  methods: {
    getPopupContainer(triggerNode) {
      return triggerNode.parentNode;
    },

    handleMenuVisible(visible) {
      if (visible) {
        this.searchText = "";
      }
      this.menuVisible = visible;
    },

    handleSearchInput(e) {
      this.searchText = e.target.value;
    },

    handleFocus(event) {
      event.target.select();
    },

    getTimeslicingName() {
      return this.OPERATOR_MAP?.TIMESLICING?.children.find((v) => v.operator === this.value.operator)?.name || "";
    },

    updateSearchText() {
      if (this.menuVisible) {
        this.searchText = "";
      } else {
        const timeslicingName = this.getTimeslicingName();
        const operatorName = this.OPERATOR_MAP[this.value.operator]?.name;
        this.searchText = timeslicingName || operatorName || "";
      }
    },

    onSelect(operator) {
      this.value.changeOperator(operator.operator);
      this.onChange && this.onChange(this.value);
      const timeslicingName = this.getTimeslicingName();
      const operatorName = this.OPERATOR_MAP[this.value.operator]?.name;
      this.searchText = timeslicingName || operatorName || "";

      // 关闭弹窗
      this.menuVisible = false;
      console.log("🚀 ~ onSelect ~ 选择后 menuVisible:", this.menuVisible);
    }
  }
};
</script>

<style scoped>
.FilterOperator {
  display: inline-block;
  width: 100%;
}

.clickWrapper {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.clickWrapper .ant-input {
  padding-right: 20px;
}

.clickWrapper .anticon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.ant-dropdown-link {
  cursor: pointer;
}
</style>
