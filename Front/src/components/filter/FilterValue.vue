<template>
  <div class="FilterValue">
    <FilterValueProvider :value="filterValueContext">
      <component
        :is="currentComponent"
        :fieldType="value.fieldType"
        :operator="value.operator"
        :fieldValue="fieldValue"
        :items="items"
        :onChange="onChangeFieldValue"
      />
    </FilterValueProvider>
  </div>
</template>

<script>
import _ from "lodash";
import moment from "moment";
import Log from "../utils/log";
import FilterContext from "./FilterContext";

// 导入所有输入组件
import {
  TextInput,
  DateInput,
  SingleInput,
  DateBetweenInput,
  NumberBetweenInput,
  TwoInput,
  EnumInput,
  MonthAndDaySelect,
  MonthDaySelect,
  AdvancedBetween,
  EmptySpan,
  DefaultInput
} from "./inputs";

// 定义月份和日期选项
const monthOption = Array.from({ length: 12 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}月`
}));

const dayOptions = Array.from({ length: 31 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}日`
}));

// FilterValueProvider组件
const FilterValueProvider = {
  name: "FilterValueProvider",
  props: {
    value: {
      type: Object,
      required: true
    }
  },
  provide() {
    return {
      filterValueContext: () => this.value
    };
  },
  render() {
    return this.$slots.default;
  }
};

export default {
  name: "FilterValue",
  components: {
    FilterValueProvider,
    TextInput,
    DateInput,
    SingleInput,
    DateBetweenInput,
    NumberBetweenInput,
    TwoInput,
    EnumInput,
    MonthAndDaySelect,
    MonthDaySelect,
    AdvancedBetween,
    EmptySpan,
    DefaultInput
  },
  inject: ["filterContext"],
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      items: [],
      fieldValue: this.value.value,
      menuVisible: false,
      debounceTimer: null,
      log: Log.getLogger("FilterValue"),
      monthOption,
      dayOptions
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    dataProvider() {
      return this.context.dataProvider || {};
    },
    filterValueContext() {
      return {
        setMenuVisible: this.setMenuVisible
      };
    },
    currentComponent() {
      const { operator } = this.value;
      switch (operator) {
        case "EQ":
        case "NE":
        case "GT":
        case "GTE":
        case "LT":
        case "LTE":
          return "SingleInput";
        case "BETWEEN":
          return "TwoInput";
        case "ADVANCED_BETWEEN":
          return "AdvancedBetween";
        case "IN":
        case "NOT_IN":
        case "LIKE":
        case "NOT_LIKE":
        case "START_WITH":
        case "NOT_START_WITH":
        case "END_WITH":
        case "NOT_END_WITH":
          return "EnumInput";
        case "IS_NOT_NULL":
        case "IS_NULL":
        case "IS_TRUE":
        case "IS_FALSE":
          return "EmptySpan";
        case "MONTH_BETWEEN":
        case "MONTH_EQ":
        case "DAY_BETWEEN":
        case "DAY_EQ":
          return "MonthAndDaySelect";
        case "DATE_FORMAT_BETWEEN":
        case "DATE_FORMAT_EQ":
          return "MonthDaySelect";
        default:
          return "DefaultInput";
      }
    }
  },
  watch: {
    "value.value"(newValue) {
      this.fieldValue = newValue;
    },
    "value.tableId"() {
      this.fetchItems();
    },
    "value.schemaId"() {
      this.fetchItems();
    },
    fieldValue(newValue) {
      this.debounceFieldValueChange(newValue);
    }
  },
  mounted() {
    // console.log("🚀 ~ FilterValue mounted ~ value:", this.value);
    // console.log(
    //   "🚀 ~ FilterValue mounted ~ currentComponent:",
    //   this.currentComponent
    // );
    // console.log(
    //   "🚀 ~ FilterValue mounted ~ components:",
    //   Object.keys(this.$options.components)
    // );
    // console.log(
    //   "🚀 ~ FilterValue mounted ~ component exists:",
    //   !!this.$options.components[this.currentComponent]
    // );
    this.fetchItems();
  },
  methods: {
    setMenuVisible(visible) {
      this.menuVisible = visible;
    },

    async fetchItems() {
      this.items = [];
      if (!this.value.isEnum) {
        return;
      }
      try {
        const _items = await this.dataProvider.getPropertyEnumList(
          this.value.tableId,
          this.value.schemaId,
          this.fieldValue
        );
        this.log.debug("fetchItem", _items);
        this.items = _items;
      } catch (error) {
        this.log.error("Failed to fetch items:", error);
      }
    },

    debounceFieldValueChange(newValue) {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(() => {
        this.handleFieldValueChange(newValue);
      }, 500);
    },

    handleFieldValueChange(debounceFieldValue) {
      this.log.debug("debounceFieldValue changed call onChange", debounceFieldValue);
      const { operator } = this.value;
      if (
        operator === "IS_NOT_NULL" ||
        operator === "IS_NULL" ||
        debounceFieldValue !== null ||
        debounceFieldValue !== undefined
      ) {
        this.onChange && this.onChange(debounceFieldValue);
      }
    },

    onChangeFieldValue(v) {
      this.log.debug("onChangeFieldValue", v, JSON.stringify(this.value));
      this.value.value = v;

      // 如果value是个枚举类型的属性，并且枚举是keyvalue形式的，需要向filterModel赋值一个showValue，方便用于回显
      if (this.value.isEnum && _.isArray(this.items) && this.items.length > 0 && this.items[0].name) {
        const itemValueMap = this.items.reduce((a, b) => {
          a[b.value] = b.name;
          return a;
        }, {});
        if (_.isArray(v)) {
          this.value.showValue = v.map((_v) => itemValueMap[_v] || _v);
        } else {
          this.value.showValue = itemValueMap[v] || v;
        }
      }
      // this.onChange && this.onChange(v);
      this.fieldValue = v;
    }
  }
};
</script>

<style scoped>
.FilterValue {
  display: block;
}

/* 确保所有输入组件的样式一致 */
.FilterValue .ant-input,
.FilterValue .ant-input-number,
.FilterValue .ant-select,
.FilterValue .ant-date-picker {
  min-width: 120px;
}

.FilterValue .ant-select-selection {
  min-height: 32px;
}

/* 范围选择器样式 */
.FilterValue .ant-calendar-range-picker {
  width: auto;
}

/* 数字范围输入样式 */
.FilterValue .number-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 月日选择器样式 */
.FilterValue .month-day-selector {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 枚举输入样式 */
.FilterValue .enum-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.FilterValue .enum-input-wrapper .ant-select {
  min-width: 150px;
}

.FilterValue .enum-input-wrapper .ant-checkbox-wrapper {
  white-space: nowrap;
}
</style>
