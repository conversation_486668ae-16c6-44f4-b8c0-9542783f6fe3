<template>
  <a-switch
    size="small"
    :checked="switchValue"
    :checkedChildren="FILTER_CONNECTOR_SWITCH_MAP.true.name"
    :unCheckedChildren="FILTER_CONNECTOR_SWITCH_MAP.false.name"
    @change="handleChange"
    :disabled="mode === 'detail'"
    class="cpntSwitch"
  />
</template>

<script>
import { Switch } from "ant-design-vue";
import FilterConfig from "./FilterConfig";

// 连接器
const FILTER_CONNECTOR = FilterConfig.connector;
// 连接器map
const FILTER_CONNECTOR_SWITCH_MAP = {
  true: FILTER_CONNECTOR[0],
  false: FILTER_CONNECTOR[1]
};

const FILTER_CONNECTOR_SWITCH_MAP_REVERSE = {};
FILTER_CONNECTOR_SWITCH_MAP_REVERSE[FILTER_CONNECTOR[0].value] = true;
FILTER_CONNECTOR_SWITCH_MAP_REVERSE[FILTER_CONNECTOR[1].value] = false;

export default {
  name: "FilterConnector",
  components: {
    "a-switch": Switch
  },
  props: {
    value: {
      type: String,
      default: "AND"
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      FILTER_CONNECTOR_SWITCH_MAP
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log })
        }
      );
    },
    mode() {
      return this.context.mode || "edit";
    },
    switchValue() {
      return FILTER_CONNECTOR_SWITCH_MAP_REVERSE[this.value];
    }
  },
  methods: {
    handleChange(checked) {
      const log = this.logProvider.getLogger("FilterConnector");
      log.debug("value", this.value);
      this.onChange(FILTER_CONNECTOR_SWITCH_MAP[checked].value);
    }
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
