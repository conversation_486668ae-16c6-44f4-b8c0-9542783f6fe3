// Vue 2 Context implementation for FilterSingleWrapper

export const FilterSingleWrapperContextProvider = {
  name: "FilterSingleWrapperContextProvider",
  props: {
    value: {
      type: Object,
      default: () => ({ editing: false })
    }
  },
  provide() {
    return {
      filterSingleWrapperContext: this.value
    };
  },
  render(h) {
    // 确保返回单一根节点
    const children = this.$slots.default;
    if (children && children.length === 1) {
      return children[0];
    } else if (children && children.length > 1) {
      // 如果有多个子节点，用 div 包装
      return h("div", children);
    } else {
      // 如果没有子节点，返回空 div
      return h("div");
    }
  }
};

export default {
  Provider: FilterSingleWrapperContextProvider
};
