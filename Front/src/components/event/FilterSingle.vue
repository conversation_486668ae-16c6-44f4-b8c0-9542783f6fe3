<template>
  <li :class="`FilterSingle ${mode}`">
    <div
      style="display: flex"
      :style="{
        display: mode !== 'edit' && !value.valid().isValid ? 'none' : 'flex'
      }"
    >
      <!-- 行为后面的时间 -->
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="eventTimeDisplayText" :useTakePlaceWidth="true" style="width: 400px">
          <FilterEventTime :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件行为 -->
      <div :class="`FilterEventAction ${mode} ${validator?.action && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="EVENT_ACTION[action]" :useTakePlaceWidth="true">
          <FilterEventAction :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件选择 -->
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="eventInfo.displayName" :useTakePlaceWidth="true">
          <FilterEventFieldSelect :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件属性 -->
      <div :class="`FilterField ${mode} ${validator?.eventAggregateProperty && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper
          :value="eventAggregateProperty.propertyType === 'TIMES' ? '次数' : eventAggregateProperty.property?.fieldName"
          :useTakePlaceWidth="true"
        >
          <FilterEventProperty :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 计数函数 -->
      <div
        v-if="eventAggregateProperty.propertyType === 'EVENT_PROPERTY'"
        :class="`FilterField ${mode} ${validator?.fun && value.validating ? 'has-error' : ''}`"
      >
        <FilterSingleWrapper :value="funValue" :useTakePlaceWidth="true">
          <FilterEventFunction :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 操作符 -->
      <div :class="`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="value.getOperatorShow()" :useTakePlaceWidth="true">
          <FilterOperator :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 值 -->
      <div
        :class="`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`"
        :style="{ display: value.isValueCanEdit() === false ? 'none' : 'block' }"
      >
        <FilterSingleWrapper :value="value.getValueShow()">
          <FilterValue :value="value" :onChange="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 时间选择 -->
      <!-- <div :class="`FilterEventSelectTime ${mode} ${validator?.dateRange && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="`${timeStr1}~${timeStr2}`">
          <FilterEventSelectTime :value="value" :onChange="onChange" :isActionCollection="isActionCollection" />
        </FilterSingleWrapper>
      </div> -->

      <!-- 控制器 -->
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip v-if="value.validating && hasValidationError" placement="topRight" :title="validationMessage">
          <div style="margin-right: 5px"><a-icon type="drag" /></div>
        </a-tooltip>
        <span
          class="handleAdd"
          @click="onAddFilter"
          :style="{
            display:
              $refs.filterRef && $refs.filterRef.getFilterCount && $refs.filterRef.getFilterCount() >= 10
                ? 'none'
                : 'inline-block'
          }"
        >
          <a-icon type="plus-circle" /> {{ action === 'DO_SEQ' ? '后续行为' : '条件' }}
        </span>
        <a-icon type="close-circle" @click="onDelete" />
      </div>
    </div>

    <!-- 内部过滤器 -->
    <BaseFilter
      ref="filterRef"
      class="innerFilter"
      :dataProvider="dataProviderComputed"
      :value="eventFilterProperty"
      :onChange="onChangeFilter"
      :mode="mode"
      :hideAdd="true"
      :hideInit="true"
      addButtonText="添加过滤条件"
    />
  </li>
</template>

<script>
import { Tooltip, Icon } from "ant-design-vue";
import _ from "lodash";
import BaseFilter from "../filter/Filter.vue";
import FilterSingleWrapper from "./FilterSingleWrapper.vue";
import FilterEventFieldSelect from "./FilterEventFieldSelect.vue";
import FilterOperator from "./FilterOperator.vue";
import FilterValue from "./FilterValue.vue";
import FilterEventFunction from "./FilterEventFunction.vue";
import FilterEventAction from "./FilterEventAction.vue";
import FilterEventSelectTime from "./FilterEventSelectTime.vue";
import FilterEventProperty from "./FilterEventProperty.vue";
import FilterEventTime from "./FilterEventTime.vue";
import FILTER_CONFIG from "./FilterConfig";
import moment from "moment";

const timeTerm = [
  {
    value: "MINUTE",
    label: "分钟",
    unit: "minutes"
  },
  {
    value: "HOUR",
    label: "小时",
    unit: "hours"
  },
  {
    value: "DAY",
    label: "天",
    unit: "days"
  },
  {
    value: "WEEK",
    label: "周",
    unit: "weeks"
  },
  {
    value: "MONTH",
    label: "月",
    unit: "months"
  }
];

const isPassObj = {
  true: "之前",
  false: "之后"
};

const getString = (obj, showTime) => {
  let str = "";
  if (typeof obj === "object") {
    if (obj.type === "ABSOLUTE") {
      const format = showTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD";
      str = `${moment(obj.timestamp).format(format)}`;
    } else if (obj.type === "RELATIVE") {
      str = `${obj.times}${timeTerm.find((n) => n.value === obj.timeTerm)?.label}${
        isPassObj[JSON.stringify(obj.isPast)]
      }`;
    } else if (obj.type === "NOW") {
      str = "现在";
    }
  }
  return str;
};

export default {
  name: "FilterSingle",
  components: {
    "a-tooltip": Tooltip,
    "a-icon": Icon,
    BaseFilter,
    FilterSingleWrapper,
    FilterEventFieldSelect,
    FilterOperator,
    FilterValue,
    FilterEventFunction,
    FilterEventAction,
    FilterEventSelectTime,
    FilterEventProperty,
    FilterEventTime
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    onDelete: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      validator: {},
      EVENT_ACTION: FILTER_CONFIG.EVENT_ACTION
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    mode() {
      return this.context.mode || "edit";
    },
    dataProvider() {
      return this.context.dataProvider || {};
    },
    isActionCollection() {
      return this.context.isActionCollection || false;
    },
    eventAggregateProperty() {
      return this.value.eventAggregateProperty || {};
    },
    action() {
      return this.value.action;
    },
    eventInfo() {
      return this.value.eventInfo || {};
    },
    dateRange() {
      return this.value.dateRange || [];
    },
    eventFilterProperty() {
      return this.value.eventFilterProperty || {};
    },
    dataProviderComputed() {
      const pickPd = _.pick(this.dataProvider, "getPropertyList");
      pickPd.eventId = this.eventInfo.id || 0;
      return pickPd;
    },
    funValue() {
      const { fun } = this.eventAggregateProperty;
      if (fun) {
        return (
          _.find(FILTER_CONFIG.CONDITIONFUN[this.eventAggregateProperty?.property?.fieldType || "INT"], (item) => {
            return item.value === fun;
          }) || {}
        ).name;
      }
      return "";
    },
    timeStr1() {
      return getString(this.dateRange[0], true);
    },
    timeStr2() {
      return getString(this.dateRange[1], true);
    },
    eventTimeDisplayText() {
      const { eventFirst, timeValue, timeUnit } = this.value;
      if (eventFirst && timeValue && timeUnit) {
        const actionText = FILTER_CONFIG.EVENT_FIRST[eventFirst] || '';
        const unitText = FILTER_CONFIG.TIME_TYPE[timeUnit] || '';
        return `${actionText} 在${timeValue}${unitText}之内`;
      }
      return "请设置时间条件";
    },
    hasValidationError() {
      return (
        this.validator?.action ||
        this.validator?.id ||
        this.validator?.eventAggregateProperty ||
        this.validator?.fun ||
        this.validator?.operator ||
        this.validator?.value ||
        this.validator?.dateRange
      );
    },
    validationMessage() {
      return _.head(_.values(this.validator.message)) || "";
    }
  },
  watch: {
    action() {
      this.updateValidator();
    },
    "eventInfo.id"() {
      this.updateValidator();
    },
    "eventAggregateProperty.value"() {
      this.updateValidator();
    },
    "eventAggregateProperty.propertyType"() {
      this.updateValidator();
    },
    dateRange: {
      handler() {
        this.updateValidator();
      },
      deep: true
    },
    "eventAggregateProperty.fun"() {
      this.updateValidator();
    },
    "eventAggregateProperty.operator"() {
      this.updateValidator();
    }
  },
  methods: {
    updateValidator() {
      if (this.mode !== "edit") return;
      this.validator = this.value.valid();
    },

    onChangeFilter(data) {
      this.value.changePropertyValue(data);
      this.onChange(this.value);
    },

    onAddFilter() {
      if (this.$refs.filterRef && this.$refs.filterRef.addFilterGroup) {
        let filters = this.$refs.filterRef.addFilterGroup();
        this.value.changePropertyValue(filters);
        this.onChange(this.value);
      }
    },

    // 暴露给父组件的方法
    isValid() {
      if (!this.$refs.filterRef) return true;
      if (_.isEmpty(this.eventFilterProperty)) return true;
      return this.$refs.filterRef.isValid && this.$refs.filterRef.isValid(true);
    }
  },
  mounted() {
    this.updateValidator();
  }
};
</script>

<style scoped>
.FilterEventSelectTime {
  width: 300px;
}
/* 样式将从 event.scss 中继承 */
</style>
