// Vue 2 Context implementation for Event Filter

export const FilterContextProvider = {
  name: "FilterContextProvider",
  props: {
    value: {
      type: Object,
      required: true
    }
  },
  provide() {
    return {
      filterContext: () => this.value || {}
    };
  },
  render(h) {
    // 确保返回单一根节点
    const children = this.$slots.default;
    if (children && children.length === 1) {
      return children[0];
    } else if (children && children.length > 1) {
      // 如果有多个子节点，用 div 包装
      return h("div", children);
    } else {
      // 如果没有子节点，返回空 div
      return h("div");
    }
  }
};

export const useFilterContext = () => {
  // This is a placeholder for Vue 2 compatibility
  // In Vue 2, we use inject in component options
  return {};
};

export default {
  Provider: FilterContextProvider,
  useContext: useFilterContext
};
