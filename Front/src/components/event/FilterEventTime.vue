<template>
  <span style="display: flex; align-items: center; gap: 8px;">
    <!-- 行为选择框 -->
    <a-select 
      :value="value.eventFirst" 
      style="width: 150px;" 
      @change="handleEventFirstChange" 
      placeholder="行为"
    >
      <a-select-option 
        v-for="(item, index) in eventFirstValues" 
        :key="item" 
        :value="item"
      >
        {{ eventFirstNames[index] }}
      </a-select-option>
    </a-select>

    <span>在</span>
    
    <!-- 时间数值输入框 -->
    <a-input-number
      :value="value.timeValue || 1"
      :min="1"
      :max="999"
      style="width: 80px;"
      @change="handleTimeValueChange"
      placeholder="时间"
    />
    
    <!-- 时间单位选择框 -->
    <a-select
      :value="value.timeUnit || 'HOUR'"
      style="width: 80px;"
      @change="handleTimeUnitChange"
    >
      <a-select-option 
        v-for="(item, index) in timeUnitValues" 
        :key="item" 
        :value="item"
      >
        {{ timeUnitNames[index] }}
      </a-select-option>
    </a-select>
    
    <span>之内</span>
  </span>
</template>

<script>
import { Select, InputNumber } from "ant-design-vue";
import _ from "lodash";
import FILTER_CONFIG from "./FilterConfig";

const eventFirstValues = _.keys(FILTER_CONFIG.EVENT_FIRST);
const eventFirstNames = _.values(FILTER_CONFIG.EVENT_FIRST);
const timeUnitValues = _.keys(FILTER_CONFIG.TIME_TYPE);
const timeUnitNames = _.values(FILTER_CONFIG.TIME_TYPE);

export default {
  name: "FilterEventTime",
  components: {
    "a-select": Select,
    "a-select-option": Select.Option,
    "a-input-number": InputNumber
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      eventFirstValues,
      eventFirstNames,
      timeUnitValues,
      timeUnitNames
    };
  },
  methods: {
    handleEventFirstChange(v) {
      this.value.changeProperty({ 
        ...this.value, 
        eventFirst: v 
      });
      this.onChange(this.value);
    },
    
    handleTimeValueChange(v) {
      this.value.changeProperty({ 
        ...this.value, 
        timeValue: v 
      });
      this.onChange(this.value);
    },
    
    handleTimeUnitChange(v) {
      this.value.changeProperty({ 
        ...this.value, 
        timeUnit: v 
      });
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
