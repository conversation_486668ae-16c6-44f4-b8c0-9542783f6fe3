<template>
  <div v-if="hasValue" class="FilterGroupListPannel1">
    <FilterGroup
      v-if="filterListCount > 0"
      :connector="value.connector"
      :onChangeConnector="onChangeConnector(value)"
      :filterCount="filterListCount"
      inner=""
    >
      <template v-for="(filterList, index) in value.filters">
        <FilterGroup
          v-if="getFilterGroupShouldRender(filterList)"
          :key="index"
          :connector="filterList.connector"
          :onChangeConnector="onChangeConnector(filterList)"
          :filterCount="getFilterCount(filterList)"
          inner="inner"
        >
          <FilterSingle
            v-for="(filter, i) in filterList.filters"
            :key="`${filter.key}`"
            :ref="`filterSingle_${index}_${i}`"
            :value="filter"
            :onChange="onChangeFilter(filterList, i)"
            :onAdd="onAddFilter(filterList, i)"
            :onDelete="onDeleteFilter(filterList, i)"
          />
        </FilterGroup>
      </template>
    </FilterGroup>
  </div>
</template>

<script>
import _ from "lodash";
import FilterGroup from "./FilterGroup.vue";
import FilterSingle from "./FilterSingle.vue";
import FilterModelUtil from "./FilterModelUtil";

export default {
  name: "FilterListGroup",
  components: {
    FilterGroup,
    FilterSingle
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  computed: {
    context() {
      return this.filterContext();
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log })
        }
      );
    },
    mode() {
      return this.context.mode || "edit";
    },
    isActionCollection() {
      return this.context.isActionCollection || false;
    },
    // 检查是否有有效的 value，与 React 版本保持一致
    hasValue() {
      console.log("hasValue", this.value);
      return !!(this.value && this.value.filters);
    },
    filterListCount() {
      return this.value.filters ? this.value.filters.length : 0;
    }
  },
  methods: {
    onChangeFilter(filterList, index) {
      return (newValue) => {
        console.log("FilterListGroup onChangeFilter 接收到:", newValue);
        // 直接调用 onChange，因为 FilterSingle 已经修改了 filter 对象
        this.onChange(this.value);
      };
    },

    onAddFilter(filterList, index) {
      return () => {
        filterList.filters = [
          ...filterList.filters.slice(0, index + 1),
          FilterModelUtil.createFilter(this.isActionCollection),
          ...filterList.filters.slice(index + 1)
        ];
        this.onChange(this.value);
      };
    },

    onDeleteFilter(filterList, index) {
      return () => {
        filterList.filters.splice(index, 1);
        FilterModelUtil.deleteEmptyFilterList(this.value);
        this.onChange(this.value);
      };
    },

    getFilterGroupShouldRender(filterList) {
      if (!filterList || !filterList.filters) return false;
      const filterCount =
        this.mode === "detail" ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length;
      return filterCount > 0;
    },

    getFilterCount(filterList) {
      return this.mode === "detail" ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length;
    },

    onChangeConnector(filter) {
      return (v) => {
        filter.connector = v;
        this.onChange(this.value);
      };
    },

    // 暴露给父组件的方法
    isValid(flag) {
      const refArr = [];
      // 收集所有 FilterSingle 的 ref
      this.value.filters.forEach((filterList, index) => {
        filterList.filters.forEach((filter, i) => {
          const ref = this.$refs[`filterSingle_${index}_${i}`];
          if (ref && ref[0]) {
            refArr.push(ref[0]);
          }
        });
      });

      const validResults = _.without(refArr, null, undefined, false);
      if (_.isEmpty(validResults)) return true;
      return validResults.map((item) => item.isValid?.() || true).reduce((a, b) => a && b, true);
    }
  },
  mounted() {
    const log = this.logProvider.getLogger("FilterListGroup");
    // 调试日志
    if (this.value) {
      log.debug("connector", this.value.connector);
      log.debug("filters", this.value.filters);
    }
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
