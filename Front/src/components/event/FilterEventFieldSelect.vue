<template>
  <div>
    <a-select
      show-search
      style="width: 100%"
      placeholder="请选择事件"
      :not-found-content="fetching ? undefined : null"
      @search="handleSearch"
      @change="onEventFilterChange"
      :value="value?.eventInfo?.id"
      allow-clear
      :dropdown-match-select-width="false"
      @dropdownVisibleChange="setDropDownOpen"
      option-label-prop="label"
      :filter-option="filterOption"
    >
      <template #notFoundContent>
        <a-icon v-if="fetching" type="loading" :spin="true" />
      </template>
      <a-select-option v-for="event in eventList" :key="event.id" :value="event.id" :label="event.name">
        <a-popover
          placement="right"
          :auto-adjust-overflow="true"
          overlay-class-name="eventInfoPopover"
          trigger="hover"
          :overlay-style="{ width: '512px' }"
        >
          <template #content>
            <div v-if="renderEventInfo(event)" v-html="renderEventInfo(event)"></div>
          </template>
          <div style="width: 100%">{{ event.name }}</div>
        </a-popover>
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import { Select, Icon, Popover } from "ant-design-vue";
import _ from "lodash";
import moment from "moment";

export default {
  name: "FilterEventFieldSelect",
  components: {
    "a-select": Select,
    "a-select-option": Select.Option,
    "a-icon": Icon,
    "a-popover": Popover
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      fetching: true,
      searchValue: this.value?.eventInfo?.displayName || "",
      dropDownOpen: false,
      eventList: [],
      lineList: [],
      debounceTimer: null
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    dataProvider() {
      return this.context.dataProvider || {};
    }
  },
  watch: {
    "value.eventInfo.displayName"(newValue) {
      this.searchValue = newValue || "";
    }
  },
  methods: {
    async init(searchText) {
      this.fetching = true;
      try {
        if (this.dataProvider.getEventList) {
          // debugger;
          const list = await this.dataProvider.getEventList(searchText);
          this.eventList = list.content || [];
        }

        if (this.dataProvider.getEventCountLogsByProjectId) {
          const lineRes = await this.dataProvider.getEventCountLogsByProjectId();
          this.lineList = lineRes || [];
        }
      } catch (error) {
        console.error("Failed to fetch event list:", error);
        this.eventList = [];
      }
      this.fetching = false;
    },

    handleSearch(value) {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(() => {
        this.searchValue = value;
        if (this.dropDownOpen) {
          this.init(value);
        }
      }, 500);
    },

    setDropDownOpen(open) {
      this.dropDownOpen = open;
      if (open) {
        this.init(this.searchValue);
      }
    },

    filterOption(inputValue, option) {
      if (option.componentOptions?.children?.key?.indexOf(inputValue) >= 0) return true;
      return false;
    },

    onEventFilterChange(v) {
      const currentEvent = _.find(this.eventList, (item) => item.id === v);
      this.value.changeProperty({
        ...this.value,
        eventInfo: !_.isEmpty(currentEvent)
          ? {
              id: currentEvent.id,
              eventType: currentEvent.eventType,
              displayName: currentEvent.name,
              eventNameValue: currentEvent.eventNameValue,
              filter: currentEvent.filter,
              specialPropertyMappingList: currentEvent.specialPropertyMappingList
            }
          : {},
        eventAggregateProperty: {},
        eventFilterProperty: null
      });
      this.onChange(this.value);
    },

    renderEventInfo(item) {
      // 简化版本，返回基本信息
      return `
        <div class="eventInfo">
          <div class="eventInfoTitle">${item.eventNameValue || item.name}</div>
          <div class="eventInfoContent">
            <div class="createInformation">
              ${item.createUserName || "Unknown"} 创建于 ${moment(item.createTime).format("YYYY-MM-DD")}
            </div>
            <div>事件类型：${item.eventType === "CUSTOM" ? "自定义事件" : "埋点事件"}</div>
          </div>
        </div>
      `;
    }
  },
  mounted() {
    this.init(this.searchValue);
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
