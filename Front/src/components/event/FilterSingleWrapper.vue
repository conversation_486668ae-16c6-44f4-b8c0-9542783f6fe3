<template>
  <FilterSingleWrapperContextProvider :value="{}">
    <div class="FilterSingleWrapper" :style="wrapperStyle">
      <div v-if="mode === 'detail'" class="valueShow">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'" class="takePlace" ref="takePlace">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'">
        <slot></slot>
      </div>
    </div>
  </FilterSingleWrapperContextProvider>
</template>

<script>
import _ from "lodash";
import { FilterSingleWrapperContextProvider } from "./FilterSingleWrapperContext";

export default {
  name: "FilterSingleWrapper",
  components: {
    FilterSingleWrapperContextProvider
  },
  props: {
    value: {
      type: [String, Number, Boolean],
      default: ""
    },
    useTakePlaceWidth: {
      type: Boolean,
      default: false
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      wrapperStyle: {}
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    mode() {
      return this.context.mode || "edit";
    },
    displayValue() {
      if (_.isNil(this.value) || _.isNaN(this.value)) {
        return "";
      }
      return this.value;
    }
  },
  watch: {
    value() {
      this.updateStyle();
    },
    mode() {
      this.updateStyle();
    }
  },
  methods: {
    updateStyle() {
      this.$nextTick(() => {
        if (this.$refs.takePlace && this.mode === "edit" && this.useTakePlaceWidth) {
          this.wrapperStyle = {
            width: Math.max(this.$refs.takePlace.clientWidth, 55) + 45 + "px"
          };
        } else {
          this.wrapperStyle = {};
        }
      });
    }
  },
  mounted() {
    this.updateStyle();
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
