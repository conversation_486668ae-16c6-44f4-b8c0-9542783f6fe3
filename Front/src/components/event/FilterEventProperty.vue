<template>
  <a-dropdown
    :get-popup-container="(triggerNode) => triggerNode.parentNode"
    :trigger="['click']"
    @visibleChange="setMenuVisible"
  >
    <template #overlay>
      <a-menu style="max-height: 400px; overflow-y: auto; max-width: 500px; overflow-x: auto">
        <a-menu-item v-if="showMenu">
          <a-icon type="loading" :spin="true" />
          loading...
        </a-menu-item>
        <template v-else>
          <template v-for="(children, level1) in propertyMap">
            <a-menu-item-group v-if="level1" :title="level1" :key="level1">
              <template v-for="(properties, level2) in children">
                <a-menu-item-group v-if="level2" :title="level2" :key="level2">
                  <a-menu-item
                    v-for="(p, i) in filteredProperties(properties)"
                    :key="`${p.level1}_${p.level2}_${p.fieldName}_${i}`"
                    @click="onSelectProperty(p)"
                  >
                    {{ p.fieldName }}[{{ p.field }}]
                  </a-menu-item>
                </a-menu-item-group>
                <template v-else>
                  <a-menu-item
                    v-for="(p, i) in filteredProperties(properties)"
                    :key="`${p.level1}_${p.level2}_${p.fieldName}_${i}`"
                    @click="onSelectProperty(p)"
                  >
                    {{ p.fieldName }}[{{ p.field }}]
                  </a-menu-item>
                </template>
              </template>
            </a-menu-item-group>
            <template v-else>
              <a-menu-item
                v-for="(p, i) in filteredProperties(children)"
                :key="`${p.level1}_${p.level2}_${p.fieldName}_${i}`"
                @click="onSelectProperty(p)"
              >
                {{ p.fieldName }}[{{ p.field }}]
              </a-menu-item>
            </template>
          </template>
        </template>
      </a-menu>
    </template>
    <div class="clickWrapper">
      <a-input
        class="ant-dropdown-link"
        placeholder="属性"
        @change="handleSearchChange"
        @focus="handleFocus"
        :value="searchText"
      />
      <a-icon type="down" style="color: rgba(0, 0, 0, 0.45); font-size: 12px" />
    </div>
  </a-dropdown>
</template>

<script>
import { Dropdown, Menu, Input, Icon } from "ant-design-vue";
import _ from "lodash";

export default {
  name: "FilterEventProperty",
  components: {
    "a-dropdown": Dropdown,
    "a-menu": Menu,
    "a-menu-item": Menu.Item,
    "a-menu-item-group": Menu.ItemGroup,
    "a-input": Input,
    "a-icon": Icon
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  inject: ["filterContext"],
  data() {
    return {
      propertyMap: {},
      searchText: "",
      menuVisible: false,
      fetching: false,
      debounceTimer: null,
      _: _
    };
  },
  computed: {
    context() {
      return this.filterContext();
    },
    logProvider() {
      return this.context.logProvider || { getLogger: () => ({ debug: console.log }) };
    },
    dataProvider() {
      return this.context.dataProvider || {};
    },
    showMenu() {
      return this.fetching && _.isEmpty(this.propertyMap);
    }
  },
  watch: {
    "value.eventAggregateProperty.propertyType"() {
      this.updateSearchText();
    },
    "value.eventAggregateProperty.property.fieldName"() {
      this.updateSearchText();
    },
    menuVisible(newValue) {
      if (newValue) {
        this.searchText = "";
        this.fetchProperties();
      } else {
        this.updateSearchText();
      }
    },
    searchText() {
      this.debounceFetchProperties();
    }
  },
  methods: {
    updateSearchText() {
      if (!this.menuVisible) {
        this.searchText =
          this.value?.eventAggregateProperty?.propertyType === "TIMES"
            ? "次数"
            : this.value?.eventAggregateProperty?.property?.fieldName || "";
      }
    },

    setMenuVisible(visible) {
      this.menuVisible = visible;
    },

    handleSearchChange(e) {
      this.searchText = e.target.value;
    },

    handleFocus(event) {
      event.target.select();
    },

    debounceFetchProperties() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(() => {
        if (this.menuVisible && this.searchText !== this.value?.eventAggregateProperty?.property?.fieldName) {
          this.fetchProperties();
        }
      }, 200);
    },

    async fetchProperties() {
      if (!this.menuVisible) return;

      this.fetching = true;
      const log = this.logProvider.getLogger("FilterEventProperty");
      log.debug("fetchPropertyList");

      try {
        let plist = [];
        if (this.dataProvider.getEventPropertyList) {
          plist = await this.dataProvider.getEventPropertyList(this.searchText, this.value?.eventInfo?.id);
        }

        if (!_.find(plist, (item) => item.field === "TIMES")) {
          plist.unshift({
            field: "TIMES",
            fieldName: "次数",
            isEnum: false,
            level1: "次数",
            level2: ""
          });
        }

        let levelMap = _.groupBy(plist, (v) => v.level1);
        _.keys(levelMap).forEach((level1) => {
          levelMap[level1] = _.groupBy(levelMap[level1], (v) => v.level2);
        });

        this.propertyMap = levelMap;
      } catch (error) {
        console.error("Failed to fetch properties:", error);
        this.propertyMap = {};
      }

      this.fetching = false;
    },

    filteredProperties(properties) {
      if (!_.isArray(properties)) return [];
      return properties.filter(
        (v) => !this.searchText || v.field.indexOf(this.searchText) >= 0 || v.fieldName.indexOf(this.searchText) >= 0
      );
    },

    onSelectProperty(property) {
      this.value.changeEventAggregateProperty({
        propertyType: property.field === "TIMES" ? "TIMES" : "EVENT_PROPERTY",
        property: property.field === "TIMES" ? {} : property,
        fun: property.field === "TIMES" ? "COUNT" : "",
        operator: "",
        value: ""
      });
      this.onChange(this.value);
      this.menuVisible = false;
    }
  },
  mounted() {
    this.updateSearchText();
  }
};
</script>

<style scoped>
.clickWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.clickWrapper .anticon {
  position: absolute;
  right: 8px;
  pointer-events: none;
}
</style>
