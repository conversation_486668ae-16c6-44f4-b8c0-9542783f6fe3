export default {
  maxFilterCount: 20,
  operatorList: [
    {
      name: '等于',
      operator: 'EQ'
    },
    {
      name: '不等于',
      operator: 'NE'
    },
    {
      name: '大于',
      operator: 'GT'
    },
    {
      name: '大于等于',
      operator: 'GTE'
    },
    {
      name: '小于',
      operator: 'LT'
    },
    {
      name: '小于等于',
      operator: 'LTE'
    },
    {
      name: '范围',
      operator: 'BETWEEN'
    },
    {
      name: '高级范围',
      operator: 'ADVANCED_BETWEEN'
    },
    {
      name: '包含',
      operator: 'IN'
    },
    {
      name: '不包含',
      operator: 'NOT_IN'
    },
    {
      name: '有值',
      operator: 'IS_NOT_NULL'
    },
    {
      name: '空值',
      operator: 'IS_NULL'
    },
    {
      name: '全部',
      operator: 'ALL'
    },
    {
      name: '匹配',
      operator: 'LIKE'
    },
    {
      name: '不匹配',
      operator: 'NOT_LIKE'
    },
    {
      name: '开头匹配',
      operator: 'START_WITH'
    },
    {
      name: '开头不匹配',
      operator: 'NOT_START_WITH'
    },
    {
      name: '结尾匹配',
      operator: 'END_WITH'
    },
    {
      name: '结尾不匹配',
      operator: 'NOT_END_WITH'
    },
    {
      name: '是',
      operator: 'IS_TRUE'
    },
    {
      name: '否',
      operator: 'IS_FALSE'
    }
  ],
  // typeOperator: {
  //   INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   STRING: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'ALL', 'LIKE', 'NOT_LIKE', 'START_WITH', 'NOT_START_WITH', 'END_WITH', 'NOT_END_WITH', 'IN', 'NOT_IN'],
  //   BOOL: ['IS_TRUE', 'IS_FALSE'],
  //   LABEL: ['IN', 'NOT_IN', 'ALL']
  // },
  typeOperator: {
    INT: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN'
    ],
    LONG: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN'
    ],
    DOUBLE: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
    LABEL: ['IN', 'NOT_IN', 'ALL']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: '且',
      value: 'AND'
    },
    {
      name: '或',
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: '请输入',
        maxLen: '最大输入500个字符'
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: '请输入',
        maxLen: '最大长度11个字符',
        regex: '请输入数字'
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: '请输入',
        maxLen: '最大长度20个字符',
        regex: '请输入数字'
      }
    },
    DOUBLE: {
      option: {
        required: true,
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: '请输入',
        maxLen: '最大长度20个字符',
        regex: '请输入浮点数字'
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: '请输入日期时间'
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: '请输入日期时间'
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: '请输入日期'
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: '请输入日期时间'
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: '请输入日期'
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: '请输入'
      }
    }
  },
  DATE_TYPE_MAP: {
    ABSOLUTE: '固定时间',
    RELATIVE: '相对时间'
  },
  relativeTimeObj: {
    0: '今天',
    1: '昨天',
    2: '前天'
  },

  // todo 暂时随便写一个
  EVENT_FIRST: {
    DO1: '当天做过',
    DO2: '先做过, 后未做过',
    DO3: '当天依次做过'
  },
  EVENT_ACTION: {
    DONE: '做过',
    NOT_DO: '未做过',
    DO_SEQ: '依次做过'
  },
  TIME_TYPE: {
    HOUR: '小时',
    MINITE: '分钟'
  },
  CONDITIONFUN: {
    INT: [
      { name: '去重数', value: 'UNIQUE_COUNT' },
      { name: '计数', value: 'COUNT' },
      { name: '总和', value: 'SUM' },
      { name: '平均值', value: 'AVG' },
      { name: '最大值', value: 'MAX' },
      { name: '最小值', value: 'MIN' }
    ],
    LONG: [
      { name: '去重数', value: 'UNIQUE_COUNT' },
      { name: '计数', value: 'COUNT' },
      { name: '总和', value: 'SUM' },
      { name: '平均值', value: 'AVG' },
      { name: '最大值', value: 'MAX' },
      { name: '最小值', value: 'MIN' }
    ],
    DOUBLE: [
      { name: '计数', value: 'COUNT' },
      { name: '总和', value: 'SUM' },
      { name: '平均值', value: 'AVG' },
      { name: '最大值', value: 'MAX' },
      { name: '最小值', value: 'MIN' }
    ],
    TIMES: [
      { name: '去重数', value: 'UNIQUE_COUNT' },
      { name: '总和', value: 'SUM' },
      { name: '平均值', value: 'AVG' },
      { name: '最大值', value: 'MAX' },
      { name: '最小值', value: 'MIN' }
    ],
    STRING: [{ name: '去重数', value: 'UNIQUE_COUNT' }],
    TIMESTAMP: [{ name: '计数', value: 'COUNT' }],
    BOOL: [{ name: '去重数', value: 'UNIQUE_COUNT' }]
  }
};
