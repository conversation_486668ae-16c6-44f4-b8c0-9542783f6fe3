<template>
  <a-select 
    :value="value.action" 
    style="width: 100%" 
    @change="handleChange" 
    placeholder="行为"
  >
    <a-select-option 
      v-for="(item, index) in optionValues" 
      :key="item" 
      :value="item"
    >
      {{ optionNames[index] }}
    </a-select-option>
  </a-select>
</template>

<script>
import { Select } from "ant-design-vue";
import _ from "lodash";
import FILTER_CONFIG from "./FilterConfig";

const optionValues = _.keys(FILTER_CONFIG.EVENT_ACTION);
const optionNames = _.values(FILTER_CONFIG.EVENT_ACTION);

export default {
  name: "FilterEventAction",
  components: {
    "a-select": Select,
    "a-select-option": Select.Option
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      optionValues,
      optionNames
    };
  },
  methods: {
    handleChange(v) {
      this.value.changeProperty({ ...this.value, action: v });
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
