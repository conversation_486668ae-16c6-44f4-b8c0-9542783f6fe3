<template>
  <a-select 
    :value="value.eventAggregateProperty?.operator" 
    style="width: 100%" 
    @change="handleChange" 
    placeholder="操作符"
  >
    <a-select-option 
      v-for="item in operatorOptions" 
      :key="item.operator" 
      :value="item.operator"
    >
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<script>
import { Select } from "ant-design-vue";
import FILTER_CONFIG from "./FilterConfig";

export default {
  name: "FilterOperator",
  components: {
    "a-select": Select,
    "a-select-option": Select.Option
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    operatorOptions() {
      const fieldType = this.getFieldType();
      const availableOperators = FILTER_CONFIG.typeOperator[fieldType] || [];
      return FILTER_CONFIG.operatorList.filter(op => 
        availableOperators.includes(op.operator)
      );
    }
  },
  methods: {
    getFieldType() {
      const { eventAggregateProperty } = this.value;
      if (eventAggregateProperty?.propertyType === 'TIMES') {
        return 'INT';
      }
      return eventAggregateProperty?.property?.fieldType || 'INT';
    },

    handleChange(v) {
      this.value.changeOperator(v);
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
