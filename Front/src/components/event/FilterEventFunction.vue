<template>
  <a-select 
    :value="value.eventAggregateProperty?.fun" 
    style="width: 100%" 
    @change="handleChange" 
    placeholder="计数函数"
  >
    <a-select-option 
      v-for="item in functionOptions" 
      :key="item.value" 
      :value="item.value"
    >
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<script>
import { Select } from "ant-design-vue";
import FILTER_CONFIG from "./FilterConfig";

export default {
  name: "FilterEventFunction",
  components: {
    "a-select": Select,
    "a-select-option": Select.Option
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    functionOptions() {
      const fieldType = this.value.eventAggregateProperty?.property?.fieldType || 'INT';
      return FILTER_CONFIG.CONDITIONFUN[fieldType] || [];
    }
  },
  methods: {
    handleChange(v) {
      this.value.eventAggregateProperty.fun = v;
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
