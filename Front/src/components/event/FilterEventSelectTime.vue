<template>
  <SelectTime showTime :data="dateRange" :isActionCollection="isActionCollection" :onChange="handleChange" />
</template>

<script>
import SelectTime from "@/components/selectTime/index.vue";

export default {
  name: "FilterEventSelectTime",
  components: {
    SelectTime
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    isActionCollection: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dateRange: this.value?.dateRange
    };
  },
  computed: {},
  methods: {
    handleChange(v) {
      this.value.changeProperty({ ...this.value, dateRange: v });
      this.onChange(this.value);
    }
  }
};
</script>

<style scoped>
</style>
