<template>
  <div class="select-time-wrapper">
    <div class="time-inputs">
      <a-popover v-model="startVisible" trigger="click" placement="bottom" :overlayStyle="{ width: '360px' }">
        <template #content>
          <TimeContent
            :data="value[0]"
            @save="(date) => onSave(date, 'start')"
            :isActionCollection="isActionCollection"
          />
        </template>
        <a-input
          :value="desc[0]"
          placeholder="请输入开始时间"
          readonly
          :style="{
            width: isAnalysis ? '30%' : '45%',
            textAlign: 'center',
            borderColor: errorFlag ? 'red' : ''
          }"
        />
      </a-popover>

      <a-input
        value="~"
        disabled
        :style="{
          width: '10%',
          borderLeft: 0,
          borderRight: 0,
          pointerEvents: 'none',
          backgroundColor: '#fff',
          textAlign: 'center',
          borderColor: errorFlag ? 'red' : '',
          padding: '4px'
        }"
      />

      <a-popover v-model="endVisible" trigger="click" placement="bottom">
        <template #content>
          <TimeContent :data="value[1]" @save="(date) => onSave(date, 'end')" />
        </template>
        <a-input
          :value="desc[1]"
          placeholder="请输入结束时间"
          readonly
          :style="{
            width: isAnalysis ? '30%' : '45%',
            textAlign: 'center',
            borderColor: errorFlag ? 'red' : ''
          }"
        />
      </a-popover>
    </div>
  </div>
</template>

<script>
import TimeContent from "./TimeContent.vue";
import moment from "moment";

export default {
  name: "SelectTime",
  components: {
    TimeContent
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    onChange: {
      type: Function,
      default: () => {}
    },
    type: {
      type: String,
      default: "ABSOLUTE"
    },
    showTime: {
      type: Boolean,
      default: true
    },
    isAnalysis: {
      type: Boolean,
      default: false
    },
    isActionCollection: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      startVisible: false,
      endVisible: false,
      value: [],
      desc: [],
      errorFlag: false
    };
  },
  watch: {
    data: {
      handler(newData) {
        this.value = newData ? [...newData] : [];
        if (newData && newData[0] && newData[1]) {
          const time1 = this.getTime(newData[0]);
          const time2 = this.getTime(newData[1]);
          this.errorFlag = time1 - time2 > 0;
        } else {
          this.errorFlag = false;
        }
      },
      immediate: true
    },
    value: {
      handler(newData) {
        this.updateDesc();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    moment,
    updateDesc() {
      const str1 = this.getString(this.value[0]);
      const str2 = this.getString(this.value[1]);
      this.desc = [str1, str2];
    },

    getString(obj) {
      if (!obj || typeof obj !== "object") return "";

      if (obj.type === "ABSOLUTE") {
        const format = this.showTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD";
        return moment(obj.timestamp).format(format);
      } else if (obj.type === "RELATIVE") {
        const termMap = { MINUTE: "分钟", HOUR: "小时", DAY: "天", WEEK: "周", MONTH: "月" };
        const pastMap = { true: "之前", false: "之后" };
        return `${obj.times}${termMap[obj.timeTerm]}${pastMap[obj.isPast]}`;
      } else if (obj.type === "NOW") {
        return "现在";
      }
      return "";
    },

    getTime(obj) {
      if (!obj) return Date.now();

      let time = moment();

      if (obj.type === "ABSOLUTE") {
        time = moment(obj.timestamp);
      } else if (obj.type === "RELATIVE") {
        const unitMap = { MINUTE: "minutes", HOUR: "hours" };
        if (obj.isPast) {
          time = moment().subtract(obj.times, unitMap[obj.timeTerm]);
        } else {
          time = moment().add(obj.times, unitMap[obj.timeTerm]);
        }
      }
      return time.valueOf();
    },

    onSave(date, type) {
      const index = type === "start" ? 0 : 1;
      this.value[index] = date;
      this.value = [...this.value];

      if (this.value[0] && this.value[1]) {
        const time1 = this.getTime(this.value[0]);
        const time2 = this.getTime(this.value[1]);
        const flag = time1 - time2 > 0;
        this.errorFlag = flag;
        this.onChange && this.onChange(this.value, flag);
      }

      // 关闭弹窗
      if (type === "start") {
        this.startVisible = false;
      } else {
        this.endVisible = false;
      }
    },

    check() {
      return !this.errorFlag;
    }
  }
};
</script>

<style scoped>
.select-time-wrapper {
  max-width: 360px;
}

.time-inputs {
  display: flex;
  align-items: center;
}

.time-inputs .ant-input {
  border-radius: 0;
}

.time-inputs .ant-input:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.time-inputs .ant-input:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
</style>
